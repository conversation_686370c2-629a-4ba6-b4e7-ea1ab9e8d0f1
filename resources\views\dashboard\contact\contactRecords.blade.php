@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / contact / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">

        <table id="contact_category_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>

                    <th style="min-width: 120px;">Name</th>
                    <th style="min-width: 120px;">Email</th>
                    <th style="min-width: 330px;">Message</th>
                    <th style="min-width: 330px;">Date</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($contactRecords as $contactRecords)
                <tr class="category_row">
                    <td></td>
                    <td>{{ $contactRecords->name }}</td>
                    <td>{{ $contactRecords->email }}</td>
                    <td>{{ $contactRecords->message }}</td>
                     <td>{{ customDate($contactRecords->created_at, 'd M Y H:i') }}</td>
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                              

                                <button class="payment_btn reply_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasReply{{$contactRecords->id}}"
                                    data-form-id="reply_{{$contactRecords->id}}" data-id="{{$contactRecords->id}}">
                                    <i class="fas fa-comment-dots"></i> Reply
                                </button>

                                <button class="detail_btn reply_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasDetail{{$contactRecords->id}}">
                                    <i class="fas fa-info"></i> Detail
                                </button>
                                  <button data-id="{{ $contactRecords->id }}" type="button"
                                    class="delete_btn delete_contact_category"><i class="fas fa-trash-alt"></i>
                                    Delete</button>
                            </div>
                        </div>
                        @include("dashboard.contact.reply")
                        @include("dashboard.contact.detail")

                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.contact.delete")


@endsection
