@extends('website.include.layout')
@section('title', 'Booking Terms And Conditions')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')


@section('content')
    <style>
        :root {
            --primary: #823602;
            --primary-light: #a35831;
            --primary-dark: #5c2501;
            --secondary: #e9b824;
            --accent: #4a7729;
            --light: #f8f9fa;
            --light-bg: #fdf8f3;
            --dark: #212529;
            --gray: #6c757d;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--light-bg);
            color: #333;
            line-height: 1.6;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23fdf8f3"/><path d="M0 50 Q 25 30, 50 50 T 100 50 L100 100 L0 100 Z" fill="%2382360220"/></svg>');
            background-size: cover;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 40px 0 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
           
        }
        
        /* header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--secondary);
        } */
        
        .logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
        }
        
        .logo-line1 {
            font-size: 2.2rem;
            font-weight: 700;
            letter-spacing: 5px;
            text-transform: uppercase;
            color: white;
            line-height: 1.2;
        }
        
        .logo-line2 {
            font-size: 3.5rem;
            font-weight: 800;
            letter-spacing: 1px;
            color: var(--secondary);
            line-height: 1;
            margin-top: -10px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto 20px;
            font-weight: 300;
        }
        
        .effective-date {
            background-color: rgba(255,255,255,0.15);
            display: inline-block;
            padding: 8px 20px;
            border-radius: 30px;
            margin-top: 15px;
            font-size: 1rem;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .header-pattern {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 10" preserveAspectRatio="none"><path d="M0,0 Q 50,8 100,0 L100,10 L0,10 Z" fill="%23fdf8f3"/></svg>');
            background-size: 100% 100%;
        }
        
        /* Terms Container */
        .terms-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.08);
            margin: 40px 0 60px;
            overflow: hidden;
            border: 1px solid rgba(130, 54, 2, 0.1);
        }
        
        .terms-nav {
            background-color: white;
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-bottom: 1px solid #eee;
        }
        
        .terms-nav ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            justify-content: center;
            gap: 8px;
        }
        
        .terms-nav a {
            color: var(--dark);
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(130, 54, 2, 0.15);
            font-weight: 500;
            display: flex;
        }
        
        .terms-nav a:hover, 
        .terms-nav a.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        .terms-content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 50px;
            padding-bottom: 40px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(130, 54, 2, 0.1);
        }
        
        .section-number {
            background: var(--primary);
            color: white;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            flex-shrink: 0;
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        h2 {
            font-size: 1.8rem;
            color: var(--primary);
            font-weight: 700;
        }
        
        h3 {
            font-size: 1.4rem;
            color: var(--primary-dark);
            margin: 25px 0 15px;
            font-weight: 600;
        }
        
        .highlight-box {
            background-color: #fdf8f3;
            border: 1px solid rgba(130, 54, 2, 0.15);
            border-left: 4px solid var(--primary);
            padding: 25px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
                    .cookie-table {
    width: 100%;
    display: block;
  overflow-x: auto;
}
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            box-shadow: 0 3px 15px rgba(0,0,0,0.03);
            border-radius: 8px;
            overflow: hidden;
        }
        
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        th {
            background-color: #fdf8f3;
            font-weight: 600;
            color: var(--primary-dark);
            font-size: 1.1rem;
        }
        
        tr:hover {
            background-color: rgba(130, 54, 2, 0.03);
        }
        
        /* Payment Methods */
        .payment-methods {
            display: flex;
            gap: 25px;
            margin: 35px 0;
            flex-wrap: wrap;
        }
        
        .method-card {
            flex: 1;
            min-width: 280px;
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 25px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .method-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: var(--primary);
        }
        
        .method-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 25px rgba(0,0,0,0.1);
        }
        
        .method-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .method-icon {
            width: 60px;
            height: 60px;
            background: rgba(130, 54, 2, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: var(--primary);
            font-size: 1.8rem;
        }
        
        .method-title {
            font-weight: 700;
            font-size: 1.3rem;
            color: var(--dark);
        }
        
        .method-details {
            font-size: 1rem;
            color: var(--gray);
            line-height: 1.7;
        }
        
        .method-details ul {
            padding-left: 25px;
            margin: 15px 0;
        }
        
        .method-details li {
            margin-bottom: 8px;
            position: relative;
        }
        
        .method-details li::before {
            content: "•";
            color: var(--primary);
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
        }
        
        .badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-right: 8px;
            margin-bottom: 10px;
        }
        
        .badge-primary {
            background-color: rgba(130, 54, 2, 0.1);
            color: var(--primary);
            border: 1px solid rgba(130, 54, 2, 0.2);
        }
        
        .badge-warning {
            background-color: rgba(233, 184, 36, 0.15);
            color: #b88d0c;
            border: 1px solid rgba(233, 184, 36, 0.2);
        }
        
        /* Contact Section */
        .contact-info {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border-radius: 12px;
            padding: 35px;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
        }
        
        .contact-info::before {
            content: "";
            position: absolute;
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.05);
        }
        
        .contact-title {
            font-size: 1.6rem;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            position: relative;
            z-index: 2;
        }
        
        .contact-items {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            margin-top: 20px;
            position: relative;
            z-index: 2;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.1rem;
        }
        
        .contact-item i {
            color: var(--secondary);
            font-size: 1.4rem;
            min-width: 30px;
            text-align: center;
        }
        
        /* Footer */
        footer {
            background-color: var(--primary-dark);
            color: rgba(255,255,255,0.8);
            text-align: center;
            padding: 40px 0 30px;
            margin-top: 20px;
            position: relative;
        }
        
        .footer-content {
            position: relative;
            z-index: 2;
        }
        
        .footer-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .footer-logo-line1 {
            font-size: 1.8rem;
            font-weight: 700;
            letter-spacing: 3px;
            color: white;
            line-height: 1.2;
        }
        
        .footer-logo-line2 {
            font-size: 2.5rem;
            font-weight: 800;
            letter-spacing: 1px;
            color: var(--secondary);
            line-height: 1;
            margin-top: -8px;
        }
        
        .copyright {
            font-size: 0.95rem;
            margin-top: 20px;
            opacity: 0.8;
        }
        
        .footer-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 10" preserveAspectRatio="none"><path d="M0,10 Q 50,2 100,10 L100,0 L0,0 Z" fill="%235c2501"/></svg>');
            background-size: 100% 100%;
        }
        
        .acceptance-box {
            background-color: #fdf8f3;
            border: 2px dashed var(--primary);
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin: 40px 0 20px;
            position: relative;
        }
        
        .acceptance-box h3 {
            color: var(--primary);
            font-size: 1.6rem;
            margin-bottom: 15px;
        }
        
        .stamp {
            position: absolute;
            top: -25px;
            right: 30px;
            width: 100px;
            height: 100px;
            background: var(--secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: var(--primary-dark);
            transform: rotate(15deg);
            border: 3px solid var(--primary);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
                .terms-nav ul{
        flex-wrap: nowrap;
        justify-content: flex-start !important;
        overflow-x: auto;
    }
    
               .terms-nav a {
        white-space: nowrap;
    }
            
            .terms-content {
                padding: 25px;
            }
            
            .payment-methods {
                flex-direction: column;
            }
            
            .contact-items {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .method-card {
                min-width: 100%;
            }
            
            .header {
                padding: 30px 0 25px;
            }
            
            .logo-line1 {
                font-size: 1.8rem;
            }
            
            .logo-line2 {
                font-size: 2.8rem;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>

    <header class="header">
        <div class="container">
            
            <h1>Booking Terms & Conditions</h1>
            <p class="subtitle">Your guide to understanding our booking policies, payment terms, and cancellation procedures</p>
            <div class="effective-date">
                <i class="fas fa-calendar-alt"></i> Effective Date: August 3, 2025
            </div>
        </div>
        <div class="header-pattern"></div>
    </header>

    <div class="container">
         <div class="terms-nav">
                <ul>
                    <li><a href="#introduction" class="active">Introduction</a></li>
                    <li><a href="#confirmation">Confirmation</a></li>
                    <li><a href="#payments">Payments</a></li>
                    <li><a href="#cancellation">Cancellation</a></li>
                    <li><a href="#modifications">Modifications</a></li>
                    <li><a href="#responsibilities">Responsibilities</a></li>
                    <li><a href="#liability">Liability</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </div>
        <div class="terms-container">
           
            
            <div class="terms-content">
                <div class="highlight-box">
                    <p><strong>Operator:</strong> Delaware Travel Africas LLC (EIN: 37-224587)</p>
                    <p><strong>Trading As:</strong> TravelAfricas.com</p>
                    <p><strong>By completing payment, you confirm understanding and acceptance of these Terms in their entirety.</strong></p>
                </div>
                
                <div id="introduction" class="section">
                    <div class="section-header">
                        <div class="section-number">1</div>
                        <h2>Introduction</h2>
                    </div>
                    <p>These Booking Terms & Conditions ("Agreement") govern all travel services booked through TravelAfricas.com ("we", "us", or "our"). By confirming your booking, you ("Traveler") agree to be bound by these terms. This Agreement supersedes all prior communications.</p>
                    <p>TravelAfricas.com specializes in creating unforgettable African travel experiences, offering both pre-designed tours and custom itineraries tailored to your preferences.</p>
                </div>
                
                <div id="confirmation" class="section">
                    <div class="section-header">
                        <div class="section-number">2</div>
                        <h2>Booking Confirmation</h2>
                    </div>
                    <h3>2.1 Reservation Requirements</h3>
                    <p>Bookings are confirmed only upon:</p>
                    <ul>
                        <li>Receipt of required deposit payment</li>
                        <li>Issuance of our official confirmation email</li>
                        <li>Completion of all required traveler information</li>
                    </ul>
                    
                    <h3>2.2 Traveler Obligations</h3>
                    <p>You warrant that all provided information (passport details, medical requirements, dietary restrictions, etc.) is accurate and complete. Failure to provide accurate information may result in additional charges or cancellation of services.</p>
                </div>
                
                <div id="payments" class="section">
                    <div class="section-header">
                        <div class="section-number">3</div>
                        <h2>Payment Terms</h2>
                    </div>
                    
                    <h3>Payment Schedule</h3>
<div class="cookie-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Due Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Deposit</td>
                                <td>30% of total cost</td>
                                <td>At time of booking</td>
                            </tr>
                            <tr>
                                <td>Final Balance</td>
                                <td>Remaining 70%</td>
                                <td>21 days before arrival date</td>
                            </tr>
                        </tbody>
                    </table>
</div>
                    <h3>Accepted Payment Methods</h3>
                    <div class="payment-methods">
                        <div class="method-card">
                            <div class="method-header">
                                <div class="method-icon">
                                    <i class="fab fa-cc-stripe"></i>
                                </div>
                                <div class="method-title">Stripe® Online Payments</div>
                            </div>
                            <div class="method-details">
                                <p><span class="badge badge-primary">Instant Confirmation</span> <span class="badge badge-warning">USD Only</span></p>
                                <p>Secure online payment processing with industry-leading security:</p>
                                <ul>
                                    <li>Visa, Mastercard, American Express accepted</li>
                                    <li>PCI DSS Level 1 compliant processing</li>
                                    <li>No raw card data stored on our servers</li>
                                    <li>Instant booking confirmation upon payment</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="method-card">
                            <div class="method-header">
                                <div class="method-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                                <div class="method-title">SWIFT Bank Transfer</div>
                            </div>
                            <div class="method-details">
                                <p><span class="badge badge-primary">2-3 Business Days</span> <span class="badge badge-warning">USD Only</span></p>
                                <p>International bank transfer option for corporate or large group bookings:</p>
                                <ul>
                                    <li>Complete bank details provided on invoice</li>
                                    <li>Must include booking reference: "[Booking ID] - [Traveler Last Name]"</li>
                                    <li>Email payment <NAME_EMAIL></li>
                                    <li>Client responsible for all transfer fees</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="highlight-box">
                        <p><strong>Payment Conditions:</strong></p>
                        <ul>
                            <li>All payments must be in USD (currency conversion fees are the responsibility of the payer)</li>
                            <li>Bookings are not confirmed until deposit payment is received and cleared</li>
                            <li>Late payments may result in automatic cancellation of services</li>
                        </ul>
                    </div>
                </div>
                
                <div id="cancellation" class="section">
                    <div class="section-header">
                        <div class="section-number">4</div>
                        <h2>Cancellation Policy</h2>
                    </div>
                    
                    <h3>Cancellation Fees Schedule</h3>
                    <div class="cookie-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Cancellation Period</th>
                                <th>Fee</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>More than 30 days before arrival</td>
                                <td>€50 per person administration fee</td>
                            </tr>
                            <tr>
                                <td>30 to 22 days before arrival</td>
                                <td>25% of total trip cost</td>
                            </tr>
                            <tr>
                                <td>21 to 8 days before arrival</td>
                                <td>50% of total trip cost</td>
                            </tr>
                            <tr>
                                <td>7 to 3 days before arrival</td>
                                <td>75% of total trip cost</td>
                            </tr>
                            <tr>
                                <td>Less than 72 hours or no-show</td>
                                <td>100% of total trip cost</td>
                            </tr>
                        </tbody>
                    </table>
                    </div>
                    <h3>Refund Processing</h3>
                    <ul>
                        <li>Refunds will be issued to the original payment method within 14 business days</li>
                        <li>Currency conversion losses during refunds are the responsibility of the traveler</li>
                        <li>Non-refundable fees (visas, permits, etc.) will be deducted from any refund amount</li>
                    </ul>
                </div>
                
                <div id="modifications" class="section">
                    <div class="section-header">
                        <div class="section-number">5</div>
                        <h2>Modifications</h2>
                    </div>
                    
                    <h3>5.1 Traveler-Requested Changes</h3>
                    <p>Modification requests must be submitted via <NAME_EMAIL>. Additional charges may apply for:</p>
                    <ul>
                        <li>Date or destination changes</li>
                        <li>Participant substitutions</li>
                        <li>Room type upgrades</li>
                        <li>Addition of extra services</li>
                    </ul>
                    <p>A €50 administration fee applies to all modification requests.</p>
                    
                    <h3>5.2 Operator-Initiated Changes</h3>
                    <p>We reserve the right to modify itineraries due to:</p>
                    <ul>
                        <li>Safety or security concerns</li>
                        <li>Force majeure events</li>
                        <li>Service provider capacity changes</li>
                        <li>Weather conditions or natural events</li>
                    </ul>
                    <p>We will provide alternative arrangements of comparable value whenever possible.</p>
                </div>
                
                <div id="responsibilities" class="section">
                    <div class="section-header">
                        <div class="section-number">6</div>
                        <h2>Traveler Responsibilities</h2>
                    </div>
                    
                    <h3>6.1 Documentation Requirements</h3>
                    <p>You must possess valid travel documents including:</p>
                    <ul>
                        <li>Passport valid for at least 6 months beyond your return date</li>
                        <li>Required visas for all countries in your itinerary</li>
                        <li>Vaccination certificates where required</li>
                        <li>Travel insurance documentation</li>
                    </ul>
                    
                    <h3>6.2 Mandatory Travel Insurance</h3>
                    <p>Proof of comprehensive coverage must be provided at least 7 days before departure. Your policy must include:</p>
                    <ul>
                        <li>Trip cancellation and interruption coverage</li>
                        <li>Medical emergencies (minimum $200,000 coverage)</li>
                        <li>Emergency evacuation and repatriation</li>
                        <li>Coverage for adventure activities included in your itinerary</li>
                    </ul>
                </div>
                
                <div id="liability" class="section">
                    <div class="section-header">
                        <div class="section-number">7</div>
                        <h2>Liability Limitations</h2>
                    </div>
                    
                    <h3>7.1 Third-Party Services</h3>
                    <p>As an intermediary for independent service providers (hotels, transport companies, activity operators), we are not liable for:</p>
                    <ul>
                        <li>Provider negligence or service failures</li>
                        <li>Itinerary deviations by service partners</li>
                        <li>Changes made by providers without notice</li>
                    </ul>
                    
                    <h3>7.2 Damages Cap</h3>
                    <p>Our maximum liability for any claim is limited to 125% of the fees paid for the affected services.</p>
                    
                    <h3>8. Force Majeure</h3>
                    <p>We are not liable for failures caused by events beyond our reasonable control including:</p>
                    <ul>
                        <li>Natural disasters</li>
                        <li>Armed conflict or political unrest</li>
                        <li>Pandemics or health emergencies</li>
                        <li>Government restrictions or border closures</li>
                        <li>Significant transportation disruptions</li>
                    </ul>
                    <p><strong>Remedies:</strong> In such cases, we will provide either future travel credit (valid for 24 months) or proportional refund where feasible.</p>
                </div>
                
                <div id="contact" class="section">
                    <div class="section-header">
                        <div class="section-number">9</div>
                        <h2>Governing Law & Contact</h2>
                    </div>
                    
                    <h3>9.1 Governing Law & Disputes</h3>
                    <p>This Agreement is governed by Delaware law (USA). Any disputes must be resolved through binding arbitration in Wilmington, DE.</p>
                    <p>Claims must be submitted in <NAME_EMAIL> within 30 days of trip completion.</p>
                    
                    <div class="contact-info">
                        <div class="contact-title">
                            <i class="fas fa-headset"></i> Contact Information
                        </div>
                        <p>For booking inquiries, modifications, or assistance</p>
                        
                        <div class="contact-items">
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <span>+251 930 014 056</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-clock"></i>
                                <span>08:00-17:00 EAT (Mon-Fri)</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Delaware Travel Africas LLC</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="acceptance-box">
                    <div class="stamp">APPROVED</div>
                    <h3>Acceptance of Terms</h3>
                    <p>By completing payment, you confirm that you have read, understood, and agreed to all the terms and conditions outlined in this document. These terms constitute the entire agreement between you and TravelAfricas.com.</p>
                </div>
            </div>
        </div>
    </div>

  

    <script>
        // Smooth scrolling for navigation
        document.querySelectorAll('.terms-nav a').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all links
                document.querySelectorAll('.terms-nav a').forEach(link => {
                    link.classList.remove('active');
                });
                
                // Add active class to clicked link
                this.classList.add('active');
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                window.scrollTo({
                    top: targetElement.offsetTop - 120,
                    behavior: 'smooth'
                });
            });
        });
        
        // Set active nav link based on scroll position
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.terms-nav a');
            
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (pageYOffset >= sectionTop - 150) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>


@endsection