@extends('website.include.layout')
@section('title', 'Privacy Policy')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')


@section('content')

   <style>
        :root {
            --primary: #823602;
            --primary-light: #a35831;
            --primary-dark: #5c2501;
            --secondary: #e9b824;
            --accent: #4a7729;
            --light: #f8f9fa;
            --light-bg: #fdf8f3;
            --dark: #212529;
            --gray: #6c757d;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--light-bg);
            color: #333;
            line-height: 1.6;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23fdf8f3"/><path d="M0 50 Q 25 30, 50 50 T 100 50 L100 100 L0 100 Z" fill="%2382360220"/></svg>');
            background-size: cover;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 40px 0 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        
        }
        
        .header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--secondary);
        }
        
        .logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
        }
        
        .logo-line1 {
            font-size: 2.2rem;
            font-weight: 700;
            letter-spacing: 5px;
            text-transform: uppercase;
            color: white;
            line-height: 1.2;
        }
        
        .logo-line2 {
            font-size: 3.5rem;
            font-weight: 800;
            letter-spacing: 1px;
            color: var(--secondary);
            line-height: 1;
            margin-top: -10px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto 20px;
            font-weight: 300;
        }
        
        .effective-date {
            background-color: rgba(255,255,255,0.15);
            display: inline-block;
            padding: 8px 20px;
            border-radius: 30px;
            margin-top: 15px;
            font-size: 1rem;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .header-pattern {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 10" preserveAspectRatio="none"><path d="M0,0 Q 50,8 100,0 L100,10 L0,10 Z" fill="%23fdf8f3"/></svg>');
            background-size: 100% 100%;
        }
        
        /* Privacy Container */
        .privacy-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.08);
            margin: 40px 0 60px;
            overflow: hidden;
            border: 1px solid rgba(130, 54, 2, 0.1);
        }
        
        .privacy-nav {
            background-color: white;
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-bottom: 1px solid #eee;
        }
        
        .privacy-nav ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            justify-content: center;
            gap: 8px;
        }
        
        .privacy-nav a {
            color: var(--dark);
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(130, 54, 2, 0.15);
            font-weight: 500;
            display: flex;
        }
        
        .privacy-nav a:hover, 
        .privacy-nav a.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        .privacy-content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 50px;
            padding-bottom: 40px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(130, 54, 2, 0.1);
        }
        
        .section-number {
            background: var(--primary);
            color: white;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            flex-shrink: 0;
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        h2 {
            font-size: 1.8rem;
            color: var(--primary);
            font-weight: 700;
        }
        
        h3 {
            font-size: 1.4rem;
            color: var(--primary-dark);
            margin: 25px 0 15px;
            font-weight: 600;
        }
        
        .highlight-box {
            background-color: #fdf8f3;
            border: 1px solid rgba(130, 54, 2, 0.15);
            border-left: 4px solid var(--primary);
            padding: 25px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            box-shadow: 0 3px 15px rgba(0,0,0,0.03);
            border-radius: 8px;
            overflow-x: auto;
        }
        .cookie-table {
    width: 100%;
    display: block;
  overflow-x: auto;
}
        
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        th {
            background-color: #fdf8f3;
            font-weight: 600;
            color: var(--primary-dark);
            font-size: 1.1rem;
        }
        
        tr:hover {
            background-color: rgba(130, 54, 2, 0.03);
        }
        
        .data-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        
        .data-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 20px;
            background: white;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.08);
        }
        
        .data-card h4 {
            color: var(--primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .data-card h4 i {
            color: var(--secondary);
        }
        
        .badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-right: 8px;
            margin-bottom: 10px;
        }
        
        .badge-primary {
            background-color: rgba(130, 54, 2, 0.1);
            color: var(--primary);
            border: 1px solid rgba(130, 54, 2, 0.2);
        }
        
        .badge-warning {
            background-color: rgba(233, 184, 36, 0.15);
            color: #b88d0c;
            border: 1px solid rgba(233, 184, 36, 0.2);
        }
        
        /* Rights Section */
        .rights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .right-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            border-left: 4px solid var(--secondary);
        }
        
        .right-card h4 {
            color: var(--primary-dark);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* Contact Section */
        .contact-info {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border-radius: 12px;
            padding: 35px;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
        }
        
        .contact-info::before {
            content: "";
            position: absolute;
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.05);
        }
        
        .contact-title {
            font-size: 1.6rem;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            position: relative;
            z-index: 2;
        }
        
        .contact-items {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            margin-top: 20px;
            position: relative;
            z-index: 2;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.1rem;
        }
        
        .contact-item i {
            color: var(--secondary);
            font-size: 1.4rem;
            min-width: 30px;
            text-align: center;
        }
        
        /* Footer */
        footer {
            background-color: var(--primary-dark);
            color: rgba(255,255,255,0.8);
            text-align: center;
            padding: 40px 0 30px;
            margin-top: 20px;
            position: relative;
        }
        
        .footer-content {
            position: relative;
            z-index: 2;
        }
        
        .footer-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .footer-logo-line1 {
            font-size: 1.8rem;
            font-weight: 700;
            letter-spacing: 3px;
            color: white;
            line-height: 1.2;
        }
        
        .footer-logo-line2 {
            font-size: 2.5rem;
            font-weight: 800;
            letter-spacing: 1px;
            color: var(--secondary);
            line-height: 1;
            margin-top: -8px;
        }
        
        .copyright {
            font-size: 0.95rem;
            margin-top: 20px;
            opacity: 0.8;
        }
        
        .footer-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 10" preserveAspectRatio="none"><path d="M0,10 Q 50,2 100,10 L100,0 L0,0 Z" fill="%235c2501"/></svg>');
            background-size: 100% 100%;
        }
        
        .principles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .principle-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            border-top: 4px solid var(--primary);
            text-align: center;
        }
        
        .principle-card i {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 20px;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .privacy-nav ul {
              
                align-items: center;
            }
            .privacy-nav a {

    white-space: nowrap;
}
    .privacy-nav ul {
      
        flex-wrap: nowrap;
        justify-content: flex-start !important;
        overflow-x: auto;
    }
            .privacy-content {
                padding: 25px;
            }
            
            .contact-items {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .header {
                padding: 30px 0 25px;
            }
            
            .logo-line1 {
                font-size: 1.8rem;
            }
            
            .logo-line2 {
                font-size: 2.8rem;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>



    <header class="header">
        <div class="container">
           
            <h1>Privacy Policy</h1>
            <p class="subtitle">Your privacy is our priority. This policy explains how we collect, use, and protect your personal information.</p>
            <div class="effective-date">
                <i class="fas fa-calendar-alt"></i> Effective Date: August 3, 2025
            </div>
        </div>
        <div class="header-pattern"></div>
    </header>

    <div class="container">
       <div class="privacy-nav">
                <ul>
                    <li><a href="#introduction" class="active">Introduction</a></li>
                    <li><a href="#data-collection">Data Collection</a></li>
                    <li><a href="#data-use">Data Use</a></li>
                    <li><a href="#data-sharing">Data Sharing</a></li>
                    <li><a href="#data-security">Data Security</a></li>
                    <li><a href="#your-rights">Your Rights</a></li>
                    <li><a href="#cookies">Cookies</a></li>
                    <li><a href="#changes">Changes</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </div>    
    <div class="privacy-container">
         
            
            <div class="privacy-content">
                <div class="highlight-box">
                    <p><strong>Operator:</strong> Delaware Travel Africas LLC (EIN: 37-224587)</p>
                    <p><strong>Trading As:</strong> TravelAfricas.com</p>
                    <p><strong>By using our services, you consent to the practices described in this Privacy Policy.</strong></p>
                </div>
                
                <div id="introduction" class="section">
                    <div class="section-header">
                        <div class="section-number">1</div>
                        <h2>Introduction</h2>
                    </div>
                    <p>TravelAfricas.com ("we", "us", or "our") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your personal information when you visit our website or use our services.</p>
                    <p>This policy applies to information we collect through our website, email, phone, and other means. By accessing or using our services, you agree to this Privacy Policy. If you do not agree with our policies and practices, please do not use our services.</p>
                    
                    <h3>Key Principles</h3>
                    <div class="principles-grid">
                        <div class="principle-card">
                            <i class="fas fa-lock"></i>
                            <h4>Security</h4>
                            <p>We implement robust security measures to protect your data from unauthorized access</p>
                        </div>
                        <div class="principle-card">
                            <i class="fas fa-balance-scale"></i>
                            <h4>Transparency</h4>
                            <p>We clearly explain what data we collect and how we use it</p>
                        </div>
                        <div class="principle-card">
                            <i class="fas fa-user-shield"></i>
                            <h4>Control</h4>
                            <p>You have rights to access, update, or delete your personal information</p>
                        </div>
                        <div class="principle-card">
                            <i class="fas fa-hand-holding-heart"></i>
                            <h4>Minimal Collection</h4>
                            <p>We only collect data necessary for providing our services</p>
                        </div>
                    </div>
                </div>
                
                <div id="data-collection" class="section">
                    <div class="section-header">
                        <div class="section-number">2</div>
                        <h2>Information We Collect</h2>
                    </div>
                    
                    <p>We collect various types of information to provide and improve our services to you:</p>
                    
                    <div class="data-types">
                        <div class="data-card">
                            <h4><i class="fas fa-user"></i> Personal Information</h4>
                            <ul>
                                <li>Full name and contact details</li>
                                <li>Passport information</li>
                                <li>Date of birth and gender</li>
                                <li>Billing and payment information</li>
                                <li>Travel preferences and requirements</li>
                            </ul>
                        </div>
                        
                        <div class="data-card">
                            <h4><i class="fas fa-globe"></i> Travel Information</h4>
                            <ul>
                                <li>Travel itineraries and bookings</li>
                                <li>Passport and visa details</li>
                                <li>Special requirements (dietary, medical)</li>
                                <li>Travel insurance information</li>
                                <li>Emergency contact details</li>
                            </ul>
                        </div>
                        
                        <div class="data-card">
                            <h4><i class="fas fa-laptop"></i> Technical Information</h4>
                            <ul>
                                <li>IP address and device information</li>
                                <li>Browser type and version</li>
                                <li>Pages visited and time spent</li>
                                <li>Referring website information</li>
                                <li>Cookies and tracking data</li>
                            </ul>
                        </div>
                        
                        <div class="data-card">
                            <h4><i class="fas fa-comments"></i> Communications</h4>
                            <ul>
                                <li>Email correspondence</li>
                                <li>Chat and messaging history</li>
                                <li>Customer service inquiries</li>
                                <li>Feedback and survey responses</li>
                                <li>Phone call recordings (with consent)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <h3>How We Collect Information</h3>
                    <ul>
                        <li><strong>Directly from you:</strong> When you make a booking, create an account, or contact us</li>
                        <li><strong>Automatically:</strong> Through cookies and similar technologies when you use our website</li>
                        <li><strong>From third parties:</strong> Such as travel partners, payment processors, and marketing partners</li>
                    </ul>
                </div>
                
                <div id="data-use" class="section">
                    <div class="section-header">
                        <div class="section-number">3</div>
                        <h2>How We Use Your Information</h2>
                    </div>
                    
                    <p>We use the information we collect for the following purposes:</p>
                    
                    <div class="cookie-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Purpose</th>
                                <th>Description</th>
                                <th>Legal Basis</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Service Delivery</td>
                                <td>Process bookings, arrange travel services, and manage your itinerary</td>
                                <td>Contractual necessity</td>
                            </tr>
                            <tr>
                                <td>Customer Support</td>
                                <td>Respond to inquiries, resolve issues, and provide assistance</td>
                                <td>Legitimate interest</td>
                            </tr>
                            <tr>
                                <td>Payment Processing</td>
                                <td>Process transactions and prevent fraudulent activities</td>
                                <td>Contractual necessity</td>
                            </tr>
                            <tr>
                                <td>Communication</td>
                                <td>Send booking confirmations, updates, and service notifications</td>
                                <td>Contractual necessity</td>
                            </tr>
                            <tr>
                                <td>Marketing</td>
                                <td>Send promotional offers and travel inspiration (with consent)</td>
                                <td>Consent</td>
                            </tr>
                            <tr>
                                <td>Improvement</td>
                                <td>Enhance our website, services, and customer experience</td>
                                <td>Legitimate interest</td>
                            </tr>
                            <tr>
                                <td>Compliance</td>
                                <td>Meet legal obligations and regulatory requirements</td>
                                <td>Legal obligation</td>
                            </tr>
                        </tbody>
                    </table>
    </div>
                    <div class="highlight-box">
                        <p><strong>Marketing Communications:</strong> We will only send you marketing materials with your explicit consent. You can opt-out at any time using the unsubscribe link in our emails or by contacting us directly.</p>
                    </div>
                </div>
                
                <div id="data-sharing" class="section">
                    <div class="section-header">
                        <div class="section-number">4</div>
                        <h2>Information Sharing and Disclosure</h2>
                    </div>
                    
                    <p>We may share your information in the following circumstances:</p>
                    
                    <h3>4.1 Service Providers</h3>
                    <p>We share necessary information with trusted partners to facilitate your travel arrangements:</p>
                    <ul>
                        <li>Hotels and accommodation providers</li>
                        <li>Transportation companies (airlines, car rentals)</li>
                        <li>Tour operators and activity providers</li>
                        <li>Payment processors and financial institutions</li>
                        <li>Travel insurance providers</li>
                    </ul>
                    
                    <h3>4.2 Legal Requirements</h3>
                    <p>We may disclose your information when required by law or in response to:</p>
                    <ul>
                        <li>Court orders or legal processes</li>
                        <li>Government requests and regulations</li>
                        <li>To protect our rights, property, or safety</li>
                        <li>To prevent fraud or security issues</li>
                    </ul>
                    
                    <h3>4.3 Business Transfers</h3>
                    <p>In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of the transaction. We will notify you of any such change.</p>
                    
                    <div class="highlight-box">
                        <p><strong>No Sale of Data:</strong> We do not sell, rent, or trade your personal information to third parties for marketing purposes without your explicit consent.</p>
                    </div>
                </div>
                
                <div id="data-security" class="section">
                    <div class="section-header">
                        <div class="section-number">5</div>
                        <h2>Data Security</h2>
                    </div>
                    
                    <p>We implement comprehensive security measures to protect your personal information:</p>
                    
                    <div class="principles-grid">
                        <div class="principle-card">
                            <i class="fas fa-shield-alt"></i>
                            <h4>Encryption</h4>
                            <p>All sensitive data is encrypted in transit and at rest using industry-standard protocols</p>
                        </div>
                        <div class="principle-card">
                            <i class="fas fa-user-lock"></i>
                            <h4>Access Controls</h4>
                            <p>Strict access controls limit employee access to personal information</p>
                        </div>
                        <div class="principle-card">
                            <i class="fas fa-fire-extinguisher"></i>
                            <h4>Firewalls</h4>
                            <p>Advanced firewall protection secures our network infrastructure</p>
                        </div>
                        <div class="principle-card">
                            <i class="fas fa-sync-alt"></i>
                            <h4>Regular Audits</h4>
                            <p>Security audits and vulnerability assessments are conducted regularly</p>
                        </div>
                    </div>
                    
                    <p>Despite our best efforts, no security measures are 100% effective. We cannot guarantee absolute security of your information but we continuously work to maintain the highest standards of protection.</p>
                    
                    <h3>Data Retention</h3>
                    <p>We retain your personal information only for as long as necessary to:</p>
                    <ul>
                        <li>Fulfill the purposes outlined in this Privacy Policy</li>
                        <li>Comply with legal obligations (e.g., tax laws)</li>
                        <li>Resolve disputes and enforce agreements</li>
                    </ul>
                    <p>Typical retention periods:</p>
                    <ul>
                        <li>Booking data: 7 years after trip completion</li>
                        <li>Marketing data: Until consent withdrawal</li>
                        <li>Website analytics: 26 months</li>
                    </ul>
                </div>
                
                <div id="your-rights" class="section">
                    <div class="section-header">
                        <div class="section-number">6</div>
                        <h2>Your Privacy Rights</h2>
                    </div>
                    
                    <p>Depending on your location, you may have the following rights regarding your personal information:</p>
                    
                    <div class="rights-grid">
                        <div class="right-card">
                            <h4><i class="fas fa-eye"></i> Right to Access</h4>
                            <p>Request a copy of the personal data we hold about you</p>
                        </div>
                        <div class="right-card">
                            <h4><i class="fas fa-edit"></i> Right to Rectification</h4>
                            <p>Request correction of inaccurate or incomplete data</p>
                        </div>
                        <div class="right-card">
                            <h4><i class="fas fa-trash-alt"></i> Right to Erasure</h4>
                            <p>Request deletion of your personal data under certain circumstances</p>
                        </div>
                        <div class="right-card">
                            <h4><i class="fas fa-ban"></i> Right to Restrict</h4>
                            <p>Request restriction of processing your data</p>
                        </div>
                        <div class="right-card">
                            <h4><i class="fas fa-download"></i> Right to Data Portability</h4>
                            <p>Request transfer of your data to another service provider</p>
                        </div>
                        <div class="right-card">
                            <h4><i class="fas fa-times-circle"></i> Right to Object</h4>
                            <p>Object to processing of your personal data</p>
                        </div>
                        <div class="right-card">
                            <h4><i class="fas fa-hand-paper"></i> Right to Withdraw Consent</h4>
                            <p>Withdraw your consent at any time where processing is based on consent</p>
                        </div>
                        <div class="right-card">
                            <h4><i class="fas fa-user-cog"></i> Right to Non-Discrimination</h4>
                            <p>Exercise your rights without discrimination</p>
                        </div>
                    </div>
                    
                    <p>To exercise any of these rights, please contact us using the information in the Contact section. We will respond to your request within 30 days.</p>
                    
                    <div class="highlight-box">
                        <p><strong>Identity Verification:</strong> For your security, we may need to verify your identity before processing certain requests. This helps prevent unauthorized access to your personal information.</p>
                    </div>
                </div>
                
                <div id="cookies" class="section">
                    <div class="section-header">
                        <div class="section-number">7</div>
                        <h2>Cookies and Tracking Technologies</h2>
                    </div>
                    
                    <p>We use cookies and similar technologies to enhance your experience on our website:</p>
                   
                    <div class="cookie-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Cookie Type</th>
                                <th>Purpose</th>
                                <th>Duration</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Essential Cookies</strong></td>
                                <td>Required for website functionality (e.g., booking process)</td>
                                <td>Session</td>
                            </tr>
                            <tr>
                                <td><strong>Performance Cookies</strong></td>
                                <td>Collect anonymous data to improve website performance</td>
                                <td>1-2 years</td>
                            </tr>
                            <tr>
                                <td><strong>Functional Cookies</strong></td>
                                <td>Remember preferences and enhance user experience</td>
                                <td>1 year</td>
                            </tr>
                            <tr>
                                <td><strong>Marketing Cookies</strong></td>
                                <td>Deliver relevant ads and measure campaign effectiveness</td>
                                <td>1-2 years</td>
                            </tr>
                        </tbody>
                    </table>
                    </div>
                    
                    <h3>Managing Cookies</h3>
                    <p>You can control cookies through your browser settings:</p>
                    <ul>
                        <li><strong>Browser Controls:</strong> Most browsers allow you to block or delete cookies</li>
                        <li><strong>Opt-Out Tools:</strong> Use industry tools like the Digital Advertising Alliance</li>
                        <li><strong>Cookie Consent:</strong> Manage preferences through our cookie consent banner</li>
                    </ul>
                    <p>Please note that disabling cookies may affect your ability to use certain features of our website.</p>
                </div>
                
                <div id="changes" class="section">
                    <div class="section-header">
                        <div class="section-number">8</div>
                        <h2>Changes to This Privacy Policy</h2>
                    </div>
                    
                    <p>We may update this Privacy Policy periodically to reflect changes in our practices or legal requirements:</p>
                    <ul>
                        <li>We will post the updated policy on our website with a new effective date</li>
                        <li>For significant changes, we will provide prominent notice</li>
                        <li>We may notify registered users via email when appropriate</li>
                    </ul>
                    <p>We encourage you to review this Privacy Policy periodically to stay informed about how we protect your information.</p>
                </div>
                
                <div id="contact" class="section">
                    <div class="section-header">
                        <div class="section-number">9</div>
                        <h2>Contact Us</h2>
                    </div>
                    
                    <p>If you have questions about this Privacy Policy or wish to exercise your privacy rights, please contact us:</p>
                    
                    <div class="contact-info">
                        <div class="contact-title">
                            <i class="fas fa-headset"></i> Data Protection Officer
                        </div>
                        
                        <div class="contact-items">
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <span>+251 930 014 056</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Delaware Travel Africas LLC</span>
                            </div>
                        </div>
                    </div>
                    
                    <p>For general inquiries, please contact our customer service <NAME_EMAIL></p>
                    
                    <h3>Complaints</h3>
                    <p>If you have concerns about how we handle your data, you may contact your local data protection authority.</p>
                </div>
                
                <div class="highlight-box">
                    <h3>Your Acceptance</h3>
                    <p>By using TravelAfricas.com, you signify your acceptance of this Privacy Policy. If you do not agree to this policy, please do not use our services. Your continued use of the website following the posting of changes to this policy will be deemed your acceptance of those changes.</p>
                </div>
            </div>
        </div>
    </div>



    <script>
        // Smooth scrolling for navigation
        document.querySelectorAll('.privacy-nav a').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all links
                document.querySelectorAll('.privacy-nav a').forEach(link => {
                    link.classList.remove('active');
                });
                
                // Add active class to clicked link
                this.classList.add('active');
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                window.scrollTo({
                    top: targetElement.offsetTop - 120,
                    behavior: 'smooth'
                });
            });
        });
        
        // Set active nav link based on scroll position
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.privacy-nav a');
            
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (pageYOffset >= sectionTop - 150) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>




        

@endsection