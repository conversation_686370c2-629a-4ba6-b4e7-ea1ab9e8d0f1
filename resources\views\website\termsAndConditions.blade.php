@extends('website.include.layout')
@section('title', 'Terms And Conditions')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')




@section('content')

    <style>
        :root {
            --primary: #823602;
            --primary-light: #a35831;
            --primary-dark: #5c2501;
            --secondary: #e9b824;
            --accent: #4a7729;
            --light: #f8f9fa;
            --light-bg: #fdf8f3;
            --dark: #212529;
            --gray: #6c757d;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--light-bg);
            color: #333;
            line-height: 1.6;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23fdf8f3"/><path d="M0 50 Q 25 30, 50 50 T 100 50 L100 100 L0 100 Z" fill="%2382360220"/></svg>');
            background-size: cover;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 40px 0 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
            
        }
        
        /* .header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--secondary);
        } */
        
        .logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
        }
        
        .logo-line1 {
            font-size: 2.2rem;
            font-weight: 700;
            letter-spacing: 5px;
            text-transform: uppercase;
            color: white;
            line-height: 1.2;
        }
        
        .logo-line2 {
            font-size: 3.5rem;
            font-weight: 800;
            letter-spacing: 1px;
            color: var(--secondary);
            line-height: 1;
            margin-top: -10px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto 20px;
            font-weight: 300;
        }
        
        .effective-date {
            background-color: rgba(255,255,255,0.15);
            display: inline-block;
            padding: 8px 20px;
            border-radius: 30px;
            margin-top: 15px;
            font-size: 1rem;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .header-pattern {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 10" preserveAspectRatio="none"><path d="M0,0 Q 50,8 100,0 L100,10 L0,10 Z" fill="%23fdf8f3"/></svg>');
            background-size: 100% 100%;
        }
        
        /* Terms Container */
        .terms-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.08);
            margin: 40px 0 60px;
            overflow: hidden;
            border: 1px solid rgba(130, 54, 2, 0.1);
        }
        
        .terms-nav {
            background-color: white;
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-bottom: 1px solid #eee;
        }
        
        .terms-nav ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            justify-content: center;
            gap: 8px;
        }
        
        .terms-nav a {
            color: var(--dark);
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(130, 54, 2, 0.15);
            font-weight: 500;
                display: flex;
        }
        
        .terms-nav a:hover, 
        .terms-nav a.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        .terms-content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 50px;
            padding-bottom: 40px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(130, 54, 2, 0.1);
        }
        
        .section-number {
            background: var(--primary);
            color: white;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            flex-shrink: 0;
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        h2 {
            font-size: 1.8rem;
            color: var(--primary);
            font-weight: 700;
        }
        
        h3 {
            font-size: 1.4rem;
            color: var(--primary-dark);
            margin: 25px 0 15px;
            font-weight: 600;
        }
        
        .highlight-box {
            background-color: #fdf8f3;
            border: 1px solid rgba(130, 54, 2, 0.15);
            border-left: 4px solid var(--primary);
            padding: 25px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
            .cookie-table {
    width: 100%;
    display: block;
  overflow-x: auto;
}
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: white;
            box-shadow: 0 3px 15px rgba(0,0,0,0.03);
            border-radius: 8px;
            overflow: hidden;
        }
        
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        th {
            background-color: #fdf8f3;
            font-weight: 600;
            color: var(--primary-dark);
            font-size: 1.1rem;
        }
        
        tr:hover {
            background-color: rgba(130, 54, 2, 0.03);
        }
        
        .badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-right: 8px;
            margin-bottom: 10px;
        }
        
        .badge-primary {
            background-color: rgba(130, 54, 2, 0.1);
            color: var(--primary);
            border: 1px solid rgba(130, 54, 2, 0.2);
        }
        
        .badge-warning {
            background-color: rgba(233, 184, 36, 0.15);
            color: #b88d0c;
            border: 1px solid rgba(233, 184, 36, 0.2);
        }
        
        /* Contact Section */
        .contact-info {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border-radius: 12px;
            padding: 35px;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
        }
        
        .contact-info::before {
            content: "";
            position: absolute;
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.05);
        }
        
        .contact-title {
            font-size: 1.6rem;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            position: relative;
            z-index: 2;
        }
        
        .contact-items {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            margin-top: 20px;
            position: relative;
            z-index: 2;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.1rem;
        }
        
        .contact-item i {
            color: var(--secondary);
            font-size: 1.4rem;
            min-width: 30px;
            text-align: center;
        }
        
        /* Footer */
        footer {
            background-color: var(--primary-dark);
            color: rgba(255,255,255,0.8);
            text-align: center;
            padding: 40px 0 30px;
            margin-top: 20px;
            position: relative;
        }
        
        .footer-content {
            position: relative;
            z-index: 2;
        }
        
        .footer-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .footer-logo-line1 {
            font-size: 1.8rem;
            font-weight: 700;
            letter-spacing: 3px;
            color: white;
            line-height: 1.2;
        }
        
        .footer-logo-line2 {
            font-size: 2.5rem;
            font-weight: 800;
            letter-spacing: 1px;
            color: var(--secondary);
            line-height: 1;
            margin-top: -8px;
        }
        
        .copyright {
            font-size: 0.95rem;
            margin-top: 20px;
            opacity: 0.8;
        }
        
        .footer-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 10" preserveAspectRatio="none"><path d="M0,10 Q 50,2 100,10 L100,0 L0,0 Z" fill="%235c2501"/></svg>');
            background-size: 100% 100%;
        }
        
        .acceptance-box {
            background-color: #fdf8f3;
            border: 2px dashed var(--primary);
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin: 40px 0 20px;
            position: relative;
        }
        
        .acceptance-box h3 {
            color: var(--primary);
            font-size: 1.6rem;
            margin-bottom: 15px;
        }
        
        .stamp {
            position: absolute;
            top: -25px;
            right: 30px;
            width: 100px;
            height: 100px;
            background: var(--secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: var(--primary-dark);
            transform: rotate(15deg);
            border: 3px solid var(--primary);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
          .terms-nav ul{
        flex-wrap: nowrap;
        justify-content: flex-start !important;
        overflow-x: auto;
    }
    
               .terms-nav a {
        white-space: nowrap;
    }
            .terms-content {
                padding: 25px;
            }
            
            .contact-items {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .header {
                padding: 30px 0 25px;
            }
            
            .logo-line1 {
                font-size: 1.8rem;
            }
            
            .logo-line2 {
                font-size: 2.8rem;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>

   <header class="header">
        <div class="container">
          
            <h1>Terms & Conditions</h1>
            <p class="subtitle">Your guide to understanding our website policies, service terms, and user agreements</p>
            <div class="effective-date">
                <i class="fas fa-calendar-alt"></i> Effective Date: August 3, 2025
            </div>
        </div>
        <div class="header-pattern"></div>
    </header>

    <div class="container">
         <div class="terms-nav">
                <ul>
                    <li><a href="#introduction" class="active">Introduction</a></li>
                    <li><a href="#definitions">Definitions</a></li>
                    <li><a href="#website-use">Website Use</a></li>
                    <li><a href="#bookings-payments">Bookings & Payments</a></li>
                    <li><a href="#intellectual-property">Intellectual Property</a></li>
                    <li><a href="#privacy">Privacy</a></li>
                    <li><a href="#liability">Liability</a></li>
                    <li><a href="#changes">Changes</a></li>
                    <li><a href="#governing-law">Governing Law</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </div>
        <div class="terms-container">
           
            
            <div class="terms-content">
                <div class="highlight-box">
                    <p><strong>Operator:</strong> Delaware Travel Africas LLC (EIN: 37-224587)</p>
                    <p><strong>Trading As:</strong> TravelAfricas.com</p>
                    <p><strong>By accessing our website or services, you confirm acceptance of these Terms in their entirety.</strong></p>
                </div>
                
                <div id="introduction" class="section">
                    <div class="section-header">
                        <div class="section-number">1</div>
                        <h2>Introduction</h2>
                    </div>
                    <p>Welcome to TravelAfricas.com. These Terms and Conditions govern your access to and use of our website and all related services. By accessing our website or utilizing our services, you acknowledge that you have read, understood, and agree to be bound by these terms. If you do not agree with any part of these terms, please refrain from using our website and services.</p>
                    <p>TravelAfricas.com specializes in creating authentic African travel experiences, offering comprehensive travel planning services, bookings, and informational content to enhance your journey.</p>
                </div>
                
                <div id="definitions" class="section">
                    <div class="section-header">
                        <div class="section-number">2</div>
                        <h2>Definitions</h2>
                    </div>
                    <p>To ensure clarity and consistency throughout these Terms and Conditions, the following terms shall have the meanings specified:</p>
                    
                   <div class="cookie-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Term</th>
                                <th>Definition</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>"We", "Us", "Our"</strong></td>
                                <td>Refers to TravelAfricas.com and its affiliated entities</td>
                            </tr>
                            <tr>
                                <td><strong>"You", "User"</strong></td>
                                <td>Refers to any individual accessing our website or utilizing our services</td>
                            </tr>
                            <tr>
                                <td><strong>"Services"</strong></td>
                                <td>Encompasses all travel-related services provided, including but not limited to itinerary planning, accommodation and tour bookings, transportation arrangements, and informational content</td>
                            </tr>
                            <tr>
                                <td><strong>"Website"</strong></td>
                                <td>Refers to TravelAfricas.com and all associated subdomains</td>
                            </tr>
                        </tbody>
                    </table>
    </div>
                </div>
                
                <div id="website-use" class="section">
                    <div class="section-header">
                        <div class="section-number">3</div>
                        <h2>Use of the Website</h2>
                    </div>
                    
                    <h3>3.1 Eligibility Requirements</h3>
                    <p>To use our website and services, you must:</p>
                    <ul>
                        <li>Be at least 18 years of age</li>
                        <li>Possess the legal capacity to enter into binding contracts</li>
                        <li>Comply with all applicable laws and regulations</li>
                    </ul>
                    
                    <h3>3.2 User Obligations</h3>
                    <p>When using our website, you agree to:</p>
                    <ul>
                        <li>Provide accurate, current, and complete information</li>
                        <li>Maintain the confidentiality of your account credentials</li>
                        <li>Update your information promptly when changes occur</li>
                    </ul>
                    
                    <h3>3.3 Prohibited Activities</h3>
                    <p>You expressly agree not to:</p>
                    <ul>
                        <li>Use our website for any unlawful purpose</li>
                        <li>Attempt to gain unauthorized access to our systems</li>
                        <li>Disrupt or interfere with website security</li>
                        <li>Engage in data mining or similar extraction activities</li>
                        <li>Transmit viruses or malicious code</li>
                    </ul>
                </div>
                
                <div id="bookings-payments" class="section">
                    <div class="section-header">
                        <div class="section-number">4</div>
                        <h2>Bookings and Payments</h2>
                    </div>
                    
                    <h3>4.1 Booking Process</h3>
                    <p>When making a booking through our site:</p>
                    <ul>
                        <li>You acknowledge and agree to our booking-specific terms</li>
                        <li>All bookings are subject to availability</li>
                        <li>We reserve the right to refuse service at our discretion</li>
                    </ul>
                    
                    <h3>4.2 Payment Terms</h3>
                    <p>Payment obligations include:</p>
                    <ul>
                        <li>Complying with payment terms displayed at time of booking</li>
                        <li>Making payments through approved methods only</li>
                        <li>Understanding that failure to pay may result in booking cancellation</li>
                    </ul>
                    
                    <h3>4.3 Cancellation and Refunds</h3>
                    <p>Our cancellation policy applies as follows:</p>
                 <div class="cookie-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Cancellation Period</th>
                                <th>Fee Applied</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>More than 30 days before arrival</td>
                                <td>€50 administration fee per person</td>
                            </tr>
                            <tr>
                                <td>30 to 22 days before arrival</td>
                                <td>25% of total trip cost</td>
                            </tr>
                            <tr>
                                <td>21 to 8 days before arrival</td>
                                <td>50% of total trip cost</td>
                            </tr>
                            <tr>
                                <td>7 to 3 days before arrival</td>
                                <td>75% of total trip cost</td>
                            </tr>
                            <tr>
                                <td>Less than 72 hours or no-show</td>
                                <td>100% of total trip cost</td>
                            </tr>
                        </tbody>
                    </table>
                    </div>
                    <p>Please note that specific booking terms may vary by service provider and will be clearly communicated during the booking process.</p>
                </div>
                
                <div id="intellectual-property" class="section">
                    <div class="section-header">
                        <div class="section-number">5</div>
                        <h2>Intellectual Property</h2>
                    </div>
                    
                    <h3>5.1 Ownership Rights</h3>
                    <p>All content on TravelAfricas.com, including but not limited to:</p>
                    <ul>
                        <li>Text, graphics, and logos</li>
                        <li>Images and photographs</li>
                        <li>Software and code</li>
                        <li>Database structures</li>
                    </ul>
                    <p>is the exclusive property of TravelAfricas.com or its content suppliers and is protected by international copyright, trademark, and other intellectual property laws.</p>
                    
                    <h3>5.2 Limited License</h3>
                    <p>We grant you a limited, non-exclusive, non-transferable license to:</p>
                    <ul>
                        <li>Access and view content for personal, non-commercial use</li>
                        <li>Make temporary copies for browsing purposes</li>
                    </ul>
                    <p>This license does not permit:</p>
                    <ul>
                        <li>Commercial use or republication</li>
                        <li>Modification or creation of derivative works</li>
                        <li>Systematic downloading or scraping of content</li>
                    </ul>
                </div>
                
                <div id="privacy" class="section">
                    <div class="section-header">
                        <div class="section-number">6</div>
                        <h2>Privacy</h2>
                    </div>
                    
                    <p>We are committed to protecting your personal information. Our Privacy Policy explains how we:</p>
                    <ul>
                        <li>Collect, use, and store your personal data</li>
                        <li>Protect your information with security measures</li>
                        <li>Comply with data protection regulations</li>
                    </ul>
                    <p>By using our website and services, you consent to the practices described in our Privacy Policy, which is incorporated by reference into these Terms and Conditions.</p>
                    
                    <div class="highlight-box">
                        <p><strong>Privacy Commitment:</strong> We implement industry-standard security measures to protect your personal information, but cannot guarantee absolute security in all circumstances. We never sell your personal data to third parties.</p>
                    </div>
                </div>
                
                <div id="liability" class="section">
                    <div class="section-header">
                        <div class="section-number">7</div>
                        <h2>Limitation of Liability</h2>
                    </div>
                    
                    <h3>7.1 Service Disclaimer</h3>
                    <p>Our website and services are provided on an "as is" and "as available" basis. To the maximum extent permitted by law:</p>
                    <ul>
                        <li>We disclaim all warranties, express or implied</li>
                        <li>We do not guarantee uninterrupted or error-free service</li>
                        <li>We are not responsible for third-party content or services</li>
                    </ul>
                    
                    <h3>7.2 Liability Limitations</h3>
                    <p>TravelAfricas.com shall not be liable for:</p>
                    <ul>
                        <li>Indirect, incidental, or consequential damages</li>
                        <li>Loss of profits, data, or business opportunities</li>
                        <li>Damages resulting from service interruptions</li>
                    </ul>
                    <p>Where liability cannot be excluded, our maximum liability to you is limited to the amount you paid for the specific service giving rise to the claim.</p>
                </div>
                
                <div id="changes" class="section">
                    <div class="section-header">
                        <div class="section-number">8</div>
                        <h2>Changes to Terms and Conditions</h2>
                    </div>
                    
                    <p>We reserve the right to modify these Terms and Conditions at any time:</p>
                    <ul>
                        <li>Changes will be effective immediately upon posting</li>
                        <li>We will update the "Effective Date" at the top of these terms</li>
                        <li>Material changes will be communicated to registered users</li>
                    </ul>
                    <p>Your continued use of our website after changes constitutes acceptance of the modified terms. We recommend reviewing these terms periodically.</p>
                </div>
                
                <div id="governing-law" class="section">
                    <div class="section-header">
                        <div class="section-number">9</div>
                        <h2>Governing Law</h2>
                    </div>
                    
                    <p>These Terms and Conditions are governed by and construed in accordance with the laws of the State of Delaware, without regard to its conflict of law principles.</p>
                    <p>Any disputes arising from these terms or your use of our services shall be subject to the exclusive jurisdiction of the courts located in Delaware.</p>
                </div>
                
                <div id="contact" class="section">
                    <div class="section-header">
                        <div class="section-number">10</div>
                        <h2>Contact Information</h2>
                    </div>
                    
                    <p>For questions regarding these Terms and Conditions, please contact us:</p>
                    
                    <div class="contact-info">
                        <div class="contact-title">
                            <i class="fas fa-headset"></i> Contact TravelAfricas.com
                        </div>
                        
                        <div class="contact-items">
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <span>+251 930 014 056</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-clock"></i>
                                <span>08:00-17:00 EAT (Mon-Fri)</span>
                            </div>
                        </div>
                    </div>
                    
                    <p>We strive to respond to all inquiries within 48 business hours. For booking-specific questions, please refer to your booking confirmation email for dedicated support channels.</p>
                </div>
                
                <div class="acceptance-box">
                    <div class="stamp">AGREED</div>
                    <h3>Acceptance of Terms</h3>
                    <p>By accessing and using TravelAfricas.com, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions in their entirety. These terms constitute the complete agreement between you and TravelAfricas.com regarding your use of our website and services.</p>
                </div>
            </div>
        </div>
    </div>



    <script>
        // Smooth scrolling for navigation
        document.querySelectorAll('.terms-nav a').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all links
                document.querySelectorAll('.terms-nav a').forEach(link => {
                    link.classList.remove('active');
                });
                
                // Add active class to clicked link
                this.classList.add('active');
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                window.scrollTo({
                    top: targetElement.offsetTop - 120,
                    behavior: 'smooth'
                });
            });
        });
        
        // Set active nav link based on scroll position
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.terms-nav a');
            
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (pageYOffset >= sectionTop - 150) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
        

@endsection