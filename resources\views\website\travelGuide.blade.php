@extends('website.include.layout')
@section('title', 'Travel Guide')

@section('meta_title', $destinationDetail->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $destinationDetail->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $destinationDetail->meta_description ?? 'Default Meta Description')
@section('h1', $destinationDetail->page_h1_heading ?? 'Default H1 Heading')


@section('content')
 <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #823602;
            --primary-light: #a35831;
            --primary-dark: #5c2501;
            --secondary: #e9b824;
            --accent: #4a7729;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --transition: all 0.4s ease;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(to bottom, #f8f9fa, #e6e9ed);
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        .header {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%), 
                        url('https://images.unsplash.com/photo-1547471080-7cc2caa01a7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            text-align: center;
            padding: 150px 0 100px;
            position: relative;
            animation: fadeIn 1.2s ease-out;
        }

        .header h1 {
            font-family: 'Playfair Display', serif;
            font-size: 4.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 0 2px 15px rgba(0,0,0,0.4);
            letter-spacing: 1px;
        }

        .header p {
            font-size: 1.5rem;
            max-width: 800px;
            margin: 0 auto 50px;
            opacity: 0.9;
            font-weight: 300;
        }

        .header-pattern {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 25px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20" preserveAspectRatio="none"><path d="M0,0 Q 50,20 100,0 L100,20 L0,20 Z" fill="%23f8f9fa"/></svg>');
            background-size: 100% 100%;
        }

        /* Navigation */
        .country-nav {
            background: #fff;
            padding: 15px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            position: sticky;
            top: 0;
            z-index: 100;
            transition: var(--transition);
        }

        .country-nav.scrolled {
            padding: 10px 0;
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
        }

        .country-nav ul {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            list-style: none;
            gap: 8px;
        }

        .country-nav a {
            color: #333;
            text-decoration: none;
            padding: 10px 22px;
            border-radius: 30px;
            font-weight: 500;
            transition: var(--transition);
            border: 1px solid #e0e0e0;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .country-nav a i {
            font-size: 0.9rem;
        }

        .country-nav a:hover, 
        .country-nav a.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(130, 54, 2, 0.2);
        }

        /* Country Sections */
        .country-section {
            padding: 90px 0;
            background: white;
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s ease, transform 0.8s ease;
        }

        .country-section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .country-section:nth-child(even) {
            background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
        }

        .country-header {
            display: flex;
            align-items: center;
            margin-bottom: 45px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--secondary);
            position: relative;
        }

        .country-header::after {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100px;
            height: 4px;
            background: var(--primary);
            border-radius: 2px;
        }

        .country-flag {
            width: 85px;
            height: 65px;
            margin-right: 25px;
            border-radius: 6px;
            box-shadow: 0 7px 20px rgba(0,0,0,0.12);
            border: 1px solid rgba(0,0,0,0.05);
            object-fit: cover;
        }

        .country-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.7rem;
            color: var(--primary);
            letter-spacing: -0.5px;
        }

        .country-intro {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            margin-bottom: 70px;
            align-items: center;
        }

        @media (max-width: 900px) {
            .country-intro {
                grid-template-columns: 1fr;
            }
        }

        .country-image {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 18px 55px rgba(0,0,0,0.15);
            height: 420px;
            background-size: cover;
            background-position: center;
            transition: var(--transition);
            position: relative;
        }

        .country-image:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }

        .country-description {
            font-size: 1.15rem;
            line-height: 1.8;
            color: #444;
        }

        .country-description p {
            margin-bottom: 25px;
        }

        .highlight-title {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            margin: 60px 0 35px;
            color: var(--primary);
            position: relative;
            padding-bottom: 18px;
        }

        .highlight-title::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 70px;
            height: 4px;
            background: var(--secondary);
            border-radius: 2px;
        }

        .highlights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 35px;
            margin-bottom: 70px;
        }

        .highlight-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 12px 35px rgba(0,0,0,0.1);
            transition: var(--transition);
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .highlight-card:hover {
            transform: translateY(-12px);
            box-shadow: 0 20px 45px rgba(0,0,0,0.18);
        }

        .highlight-image {
            height: 220px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .highlight-image::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
        }

        .highlight-content {
            padding: 28px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .highlight-content h4 {
            font-size: 1.45rem;
            margin-bottom: 18px;
            color: #333;
        }

        .highlight-content p {
            color: #666;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .travel-tips {
            background: linear-gradient(to right, #fdf8f3 0%, #fefcf9 100%);
            border-left: 5px solid var(--secondary);
            padding: 30px;
            border-radius: 0 10px 10px 0;
            margin: 45px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
        }

        .travel-tips h3 {
            font-family: 'Playfair Display', serif;
            color: var(--primary);
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .travel-tips ul {
            padding-left: 25px;
        }

        .travel-tips li {
            margin-bottom: 12px;
            position: relative;
            padding-left: 20px;
        }

        .travel-tips li::before {
            content: "•";
            color: var(--secondary);
            font-size: 1.4rem;
            position: absolute;
            left: 0;
            top: -3px;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: var(--primary);
            color: white;
            padding: 16px 40px;
            border-radius: 35px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            margin-top: 25px;
            border: 2px solid var(--primary);
            font-size: 1.1rem;
            box-shadow: 0 5px 20px rgba(130, 54, 2, 0.25);
        }

        .cta-button:hover {
            background: transparent;
            color: var(--primary);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(130, 54, 2, 0.3);
        }

        /* Footer */
        footer {
            background: linear-gradient(to bottom, #141414 0%, #1a1a1a 100%);
            color: white;
            padding: 80px 0 40px;
            text-align: center;
            position: relative;
        }

        footer::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 15px;
            background: linear-gradient(to right, var(--primary), var(--secondary));
        }

        .footer-content {
            max-width: 850px;
            margin: 0 auto;
        }

        .footer-logo {
            font-family: 'Playfair Display', serif;
            font-size: 2.8rem;
            margin-bottom: 25px;
            color: var(--secondary);
            letter-spacing: 1px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 35px;
            margin: 35px 0;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: #ccc;
            text-decoration: none;
            transition: var(--transition);
            position: relative;
            padding-bottom: 5px;
        }

        .footer-links a::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--secondary);
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: var(--secondary);
        }

        .footer-links a:hover::after {
            width: 100%;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 18px;
            margin: 35px 0;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #2a2a2a;
            color: white;
            font-size: 1.3rem;
            transition: var(--transition);
        }

        .social-links a:hover {
            background: var(--secondary);
            color: var(--primary);
            transform: translateY(-5px);
        }

        .copyright {
            margin-top: 50px;
            padding-top: 25px;
            border-top: 1px solid #333;
            color: #999;
            font-size: 1rem;
        }

        /* Back to top */
        .back-to-top {
            position: fixed;
            bottom: 35px;
            right: 35px;
            width: 55px;
            height: 55px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            text-decoration: none;
            opacity: 0;
            transition: var(--transition);
            z-index: 99;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }

        .back-to-top.visible {
            opacity: 1;
        }

        .back-to-top:hover {
            background: var(--secondary);
            transform: translateY(-5px) scale(1.1);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .country-section:nth-child(1) { transition-delay: 0.1s; }
        .country-section:nth-child(2) { transition-delay: 0.2s; }
        .country-section:nth-child(3) { transition-delay: 0.3s; }
        .country-section:nth-child(4) { transition-delay: 0.4s; }
        .country-section:nth-child(5) { transition-delay: 0.5s; }
        .country-section:nth-child(6) { transition-delay: 0.6s; }
        .country-section:nth-child(7) { transition-delay: 0.7s; }
        .country-section:nth-child(8) { transition-delay: 0.8s; }
        .country-section:nth-child(9) { transition-delay: 0.9s; }
        .country-section:nth-child(10) { transition-delay: 1.0s; }
        .country-section:nth-child(11) { transition-delay: 1.1s; }
        .country-section:nth-child(12) { transition-delay: 1.2s; }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .header {
                padding: 100px 0 70px;
            }
            
            .header h1 {
                font-size: 3.2rem;
            }
            
            .header p {
                font-size: 1.2rem;
                padding: 0 20px;
            }
            
            .country-nav ul {
                      gap: 5px;
        flex-wrap: nowrap;
        justify-content: flex-start;
        overflow-x: auto;
            padding: 5px 0px;        
    }
            
            .country-nav a {
                padding: 8px 15px;
                font-size: 0.85rem;
                        white-space: nowrap;
        display: flex;
            }
            
            .country-title {
                font-size: 2.2rem;
            }
            
            .country-flag {
                width: 70px;
                height: 50px;
            }
            
            .highlight-title {
                font-size: 1.8rem;
            }
            
            .footer-logo {
                font-size: 2.2rem;
            }
        }

        @media (max-width: 480px) {
            header h1 {
                font-size: 2.5rem;
            }
            
            .country-title {
                font-size: 1.9rem;
            }
            
            .country-flag {
                width: 60px;
                height: 45px;
                margin-right: 15px;
            }
            
            .highlights-grid {
                grid-template-columns: 1fr;
            }
            
            .footer-links {
                gap: 20px;
                flex-direction: column;
            }
        }
    </style>

    <header class="header">
        <div class="container">
            <h1>Discover Africa's Treasures</h1>
            <p>Explore the diverse landscapes, rich cultures, and unforgettable experiences across our featured destinations</p>
        </div>
        <div class="header-pattern"></div>
    </header>

    <nav class="country-nav">
        <div class="container">
            <ul>
                <li><a href="#ethiopia" class="active"><i class="fas fa-mountain"></i> Ethiopia</a></li>
                <li><a href="#kenya"><i class="fas fa-paw"></i> Kenya</a></li>
                <li><a href="#south-africa"><i class="fas fa-tree"></i> South Africa</a></li>
                <li><a href="#uganda"><i class="fas fa-hippo"></i> Uganda</a></li>
                <li><a href="#tanzania"><i class="fas fa-camera"></i> Tanzania</a></li>
                <li><a href="#zanzibar"><i class="fas fa-umbrella-beach"></i> Zanzibar</a></li>
                <li><a href="#djibouti"><i class="fas fa-water"></i> Djibouti</a></li>
                <li><a href="#rwanda"><i class="fas fa-hiking"></i> Rwanda</a></li>
                <li><a href="#egypt"><i class="fas fa-monument"></i> Egypt</a></li>
                <li><a href="#madagascar"><i class="fas fa-leaf"></i> Madagascar</a></li>
                <li><a href="#zimbabwe"><i class="fas fa-waterfall"></i> Zimbabwe</a></li>
                <li><a href="#mozambique"><i class="fas fa-fish"></i> Mozambique</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <!-- Ethiopia -->
        <section id="ethiopia" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/et.svg" alt="Ethiopia Flag" class="country-flag">
                    <h2 class="country-title">Ethiopia - Land of Origins</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1593111774240-d529f12f5d84?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Ethiopia, the only African country never colonized, boasts a history stretching back to the dawn of humanity. Home to ancient kingdoms, rock-hewn churches, and the source of the Blue Nile, Ethiopia offers a unique blend of history, culture, and breathtaking landscapes.</p>
                        <p>From the Simien Mountains' jagged peaks to the Danakil Depression's otherworldly landscapes, Ethiopia's natural beauty is as diverse as its cultural heritage. Discover the ancient city of Axum, the medieval castles of Gondar, and the vibrant cultures of the Omo Valley.</p>
                        <a href="#" class="cta-button">Explore Ethiopia Tours <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Ethiopia Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1571167366136-b57e07761625?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Lalibela's Rock-Hewn Churches</h4>
                            <p>Visit the 11 medieval monolithic churches carved directly into the rock, a UNESCO World Heritage site and one of Christianity's holiest sites.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1626785774573-4b799315345d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Simien Mountains National Park</h4>
                            <p>Trek through this dramatic landscape, home to rare wildlife including the Gelada baboon and Ethiopian wolf.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1559563362-c667ba5f5480?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Omo Valley Tribes</h4>
                            <p>Experience the diverse cultures of Ethiopia's southern tribes, each with unique traditions, body art, and ceremonies.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Ethiopia</h3>
                    <ul>
                        <li><strong>Best time to visit:</strong> October to June (dry season)</li>
                        <li><strong>Visa:</strong> Available on arrival for most nationalities</li>
                        <li><strong>Health:</strong> Yellow fever vaccination recommended</li>
                        <li><strong>Culture:</strong> Dress modestly, especially when visiting religious sites</li>
                        <li><strong>Cuisine:</strong> Try injera (sourdough flatbread) with various stews</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Kenya -->
        <section id="kenya" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/ke.svg" alt="Kenya Flag" class="country-flag">
                    <h2 class="country-title">Kenya - Wildlife Paradise</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1598885152967-3b8b5d7d3a8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Kenya is synonymous with safari adventures. From the vast savannas of the Maasai Mara to the snow-capped peaks of Mount Kenya, this East African nation offers unparalleled wildlife experiences and stunning landscapes.</p>
                        <p>Witness the Great Migration, where millions of wildebeest and zebra cross the Mara River, meet the Maasai people and learn about their ancient traditions, or relax on the pristine beaches of the Swahili Coast. Kenya's diverse offerings make it a perfect destination for adventure seekers and relaxation lovers alike.</p>
                        <a href="#" class="cta-button">Discover Kenya Safaris <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Kenya Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1547471080-7cc2caa01a7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Maasai Mara National Reserve</h4>
                            <p>Home to the Great Migration and abundant wildlife including the Big Five.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1589555186384-8b995b1d0ac4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Amboseli National Park</h4>
                            <p>Famous for its large elephant herds and spectacular views of Mount Kilimanjaro.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1573848953384-3be02021eb0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Diani Beach</h4>
                            <p>Pristine white sand beaches and turquoise waters perfect for relaxation and water sports.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Kenya</h3>
                    <ul>
                        <li><strong>Best time for migration:</strong> July to October</li>
                        <li><strong>Visa:</strong> eVisa available online</li>
                        <li><strong>Currency:</strong> Kenyan Shilling (KES)</li>
                        <li><strong>Health:</strong> Malaria prophylaxis recommended</li>
                        <li><strong>Culture:</strong> Learn a few Swahili phrases like "Jambo" (Hello)</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- South Africa -->
        <section id="south-africa" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/za.svg" alt="South Africa Flag" class="country-flag">
                    <h2 class="country-title">South Africa - A World in One Country</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1523805009345-7448845a9e53?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>South Africa offers an extraordinary variety of experiences. From cosmopolitan Cape Town to the wildlife-rich Kruger National Park, from the Garden Route's scenic beauty to the historic sites of Johannesburg, South Africa truly is a world in one country.</p>
                        <p>Experience the vibrant cultures of its diverse population, taste world-class wines in the Cape Winelands, dive with great white sharks, or explore the stunning Drakensberg Mountains. South Africa's blend of nature, culture, and adventure creates unforgettable journeys.</p>
                        <a href="#" class="cta-button">Plan Your South Africa Trip <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">South Africa Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1507699622108-4be3abd695ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Table Mountain, Cape Town</h4>
                            <p>Take a cable car or hike up this iconic flat-topped mountain for panoramic views.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1570891836654-d4961a7b6929?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Kruger National Park</h4>
                            <p>One of Africa's largest game reserves with an incredible diversity of wildlife.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1592409065737-a253f290c70e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Garden Route</h4>
                            <p>A scenic stretch along the southern coast with forests, lagoons, and beaches.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for South Africa</h3>
                    <ul>
                        <li><strong>Best time to visit:</strong> May to September (dry season for safari)</li>
                        <li><strong>Visa:</strong> Check requirements based on nationality</li>
                        <li><strong>Currency:</strong> South African Rand (ZAR)</li>
                        <li><strong>Safety:</strong> Be aware of your surroundings in urban areas</li>
                        <li><strong>Cuisine:</strong> Try braai (barbecue) and bobotie (spiced minced meat)</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Uganda -->
        <section id="uganda" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/ug.svg" alt="Uganda Flag" class="country-flag">
                    <h2 class="country-title">Uganda - Pearl of Africa</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1569870499705-504209102861?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Winston Churchill called Uganda "the Pearl of Africa" for good reason. This lush, green country offers some of Africa's most diverse landscapes, from mist-covered mountains to vast lakes and tropical rainforests.</p>
                        <p>Uganda is renowned for its mountain gorilla trekking experiences in Bwindi Impenetrable Forest, but it also offers chimpanzee tracking, exceptional birdwatching, and the source of the Nile River. With friendly people and stunning scenery, Uganda provides an authentic African adventure.</p>
                        <a href="#" class="cta-button">Discover Uganda Adventures <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Uganda Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1559563362-c667ba5f5480?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Bwindi Impenetrable Forest</h4>
                            <p>Trek through dense jungle to encounter endangered mountain gorillas in their natural habitat.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1569168829899-3a4e5d6b4e2e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Murchison Falls</h4>
                            <p>Witness the Nile River forced through a narrow gorge, creating one of Africa's most powerful waterfalls.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1611892440504-42a792e24d32?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Queen Elizabeth National Park</h4>
                            <p>Home to tree-climbing lions, elephants, and chimpanzees in diverse ecosystems.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Uganda</h3>
                    <ul>
                        <li><strong>Gorilla permits:</strong> Book at least 6 months in advance</li>
                        <li><strong>Best time to visit:</strong> June to August and December to February</li>
                        <li><strong>Health:</strong> Yellow fever vaccination required</li>
                        <li><strong>Currency:</strong> Ugandan Shilling (UGX)</li>
                        <li><strong>Culture:</strong> Greetings are important - take time to say hello</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Tanzania -->
        <section id="tanzania" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/tz.svg" alt="Tanzania Flag" class="country-flag">
                    <h2 class="country-title">Tanzania - Safari Heartland</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-**********-2782fc586c9a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Tanzania is home to some of Africa's most iconic wildlife experiences, including the vast plains of the Serengeti, the spectacular Ngorongoro Crater, and Africa's highest peak, Mount Kilimanjaro.</p>
                        <p>From the annual wildebeest migration to the pristine beaches of its coastline, Tanzania offers a complete African experience. The country's diverse landscapes range from savannas and mountains to tropical islands, all rich with wildlife and cultural heritage.</p>
                        <a href="#" class="cta-button">Explore Tanzania Safaris <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Tanzania Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1599735097688-56cdfa3a68f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Serengeti National Park</h4>
                            <p>Witness the Great Migration across endless plains teeming with wildlife.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1614531346263-4e8a335d89ee?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Ngorongoro Crater</h4>
                            <p>Explore the world's largest intact volcanic caldera, home to dense wildlife populations.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1589555186384-8b995b1d0ac4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Mount Kilimanjaro</h4>
                            <p>Challenge yourself to reach the "Roof of Africa," the continent's highest peak.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Tanzania</h3>
                    <ul>
                        <li><strong>Best time for safari:</strong> June to October (dry season)</li>
                        <li><strong>Visa:</strong> Available on arrival for most nationalities</li>
                        <li><strong>Currency:</strong> Tanzanian Shilling (TZS), US dollars widely accepted</li>
                        <li><strong>Health:</strong> Malaria prophylaxis and yellow fever vaccination recommended</li>
                        <li><strong>Culture:</strong> Respect local customs - ask before taking photos of people</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Zanzibar -->
        <section id="zanzibar" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/1/11/Flag_of_Zanzibar.svg/1280px-Flag_of_Zanzibar.svg.png" alt="Zanzibar Flag" class="country-flag">
                    <h2 class="country-title">Zanzibar - Spice Island Paradise</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1599735097688-56cdfa3a68f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Zanzibar, an archipelago off the coast of Tanzania, is a tropical paradise with a rich cultural heritage. Known as the "Spice Islands," Zanzibar offers pristine white-sand beaches, turquoise waters, and a fascinating blend of African, Arab, Indian, and European influences.</p>
                        <p>Explore the labyrinthine streets of Stone Town, snorkel in crystal-clear waters, visit spice plantations, or simply relax on some of the world's most beautiful beaches. Zanzibar is the perfect complement to a Tanzanian safari adventure.</p>
                        <a href="#" class="cta-button">Discover Zanzibar Getaways <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Zanzibar Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-**********-2782fc586c9a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Stone Town</h4>
                            <p>Wander through this UNESCO World Heritage site with its Arab, Persian, and Indian architecture.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1573848953384-3be02021eb0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Nungwi Beach</h4>
                            <p>Relax on powdery white sands with crystal-clear turquoise waters perfect for swimming.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1592409065737-a253f290c70e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Spice Tour</h4>
                            <p>Discover the island's agricultural heritage with a tour of fragrant spice plantations.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Zanzibar</h3>
                    <ul>
                        <li><strong>Best time to visit:</strong> June to October (dry season)</li>
                        <li><strong>Culture:</strong> Dress modestly in Stone Town and villages</li>
                        <li><strong>Activities:</strong> Excellent diving and snorkeling opportunities</li>
                        <li><strong>Cuisine:</strong> Try Zanzibar pizza and fresh seafood</li>
                        <li><strong>Transport:</strong> Use local dala-dala minibuses for authentic experience</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Djibouti -->
        <section id="djibouti" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/dj.svg" alt="Djibouti Flag" class="country-flag">
                    <h2 class="country-title">Djibouti - Land of Contrasts</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1589555186384-8b995b1d0ac4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Djibouti, at the crossroads of Africa and the Middle East, offers a unique blend of cultures and landscapes. This small but fascinating country features volcanic deserts, salt lakes, and some of the world's most spectacular diving spots.</p>
                        <p>From the otherworldly landscapes of Lac Abbé to the whale sharks of the Gulf of Tadjoura, Djibouti provides off-the-beaten-path adventures. Its strategic location has created a melting pot of Somali, Afar, Arab, and French influences.</p>
                        <a href="#" class="cta-button">Explore Djibouti Adventures <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Djibouti Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1570891836654-d4961a7b6929?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Lake Assal</h4>
                            <p>Visit Africa's lowest point and the world's saltiest lake outside Antarctica.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1592409065737-a253f290c70e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Gulf of Tadjoura</h4>
                            <p>Swim with whale sharks in one of the world's best locations for these gentle giants.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1507699622108-4be3abd695ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Lac Abbé</h4>
                            <p>Explore this surreal landscape of limestone chimneys and mineral springs resembling another planet.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Djibouti</h3>
                    <ul>
                        <li><strong>Best time to visit:</strong> October to April (cooler months)</li>
                        <li><strong>Visa:</strong> Available on arrival at the airport</li>
                        <li><strong>Currency:</strong> Djiboutian Franc (DJF)</li>
                        <li><strong>Health:</strong> Drink bottled water and protect against the sun</li>
                        <li><strong>Culture:</strong> French and Arabic are widely spoken</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Rwanda -->
        <section id="rwanda" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/rw.svg" alt="Rwanda Flag" class="country-flag">
                    <h2 class="country-title">Rwanda - Land of a Thousand Hills</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1611892440504-42a792e24d32?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Rwanda, known as the "Land of a Thousand Hills," is one of Africa's most remarkable success stories. This small, mountainous country has transformed itself into one of the continent's cleanest, safest, and most progressive nations.</p>
                        <p>Rwanda is world-renowned for its mountain gorilla trekking experiences, but it also offers vibrant culture, stunning landscapes, and remarkable wildlife. From the bustling capital Kigali to the tranquil shores of Lake Kivu, Rwanda offers a unique and uplifting travel experience.</p>
                        <a href="#" class="cta-button">Plan Your Rwanda Journey <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Rwanda Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1569168829899-3a4e5d6b4e2e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Volcanoes National Park</h4>
                            <p>Trek through bamboo forests to encounter endangered mountain gorillas in their natural habitat.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1592409065737-a253f290c70e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Akagera National Park</h4>
                            <p>Experience classic savanna safari with lions, elephants, giraffes, and diverse birdlife.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1573848953384-3be02021eb0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Lake Kivu</h4>
                            <p>Relax on the shores of Africa's highest lake, surrounded by terraced hills and fishing villages.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Rwanda</h3>
                    <ul>
                        <li><strong>Gorilla permits:</strong> Book at least 6-9 months in advance ($1,500 per person)</li>
                        <li><strong>Best time to visit:</strong> June to September and December to February (dry seasons)</li>
                        <li><strong>Health:</strong> Yellow fever vaccination certificate required</li>
                        <li><strong>Culture:</strong> Be respectful when discussing the country's history</li>
                        <li><strong>Environment:</strong> Plastic bags are banned - pack accordingly</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Egypt -->
        <section id="egypt" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/eg.svg" alt="Egypt Flag" class="country-flag">
                    <h2 class="country-title">Egypt - Land of Pharaohs</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1503177119275-0aa32b3a9368?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Egypt, the cradle of civilization, is home to some of the world's most iconic ancient monuments. From the Great Pyramids of Giza to the temples of Luxor and the treasures of King Tut, Egypt offers an unparalleled journey through history.</p>
                        <p>Beyond its ancient wonders, Egypt boasts stunning Red Sea beaches, vibrant bazaars, and the life-giving Nile River. Experience the bustling streets of Cairo, cruise the Nile in traditional feluccas, and explore the underwater wonders of the Red Sea.</p>
                        <a href="#" class="cta-button">Discover Egypt Tours <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Egypt Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1503177119275-0aa32b3a9368?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Pyramids of Giza</h4>
                            <p>Marvel at the last remaining wonder of the ancient world and the enigmatic Sphinx.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1568084680786-a84f91d1153c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Valley of the Kings</h4>
                            <p>Explore the tombs of pharaohs including Tutankhamun in this ancient burial ground.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1591382386627-349b692688ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Red Sea Riviera</h4>
                            <p>Dive or snorkel in some of the world's most spectacular coral reefs teeming with marine life.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Egypt</h3>
                    <ul>
                        <li><strong>Best time to visit:</strong> October to April (cooler months)</li>
                        <li><strong>Visa:</strong> Available on arrival for most nationalities</li>
                        <li><strong>Culture:</strong> Dress modestly, especially outside tourist areas</li>
                        <li><strong>Cuisine:</strong> Try koshari (national dish) and fresh falafel</li>
                        <li><strong>Transport:</strong> Use reputable guides for desert excursions</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Madagascar -->
        <section id="madagascar" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/mg.svg" alt="Madagascar Flag" class="country-flag">
                    <h2 class="country-title">Madagascar - Island of Biodiversity</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1575380581670-6a1d4c7b1e3a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Madagascar, the world's fourth largest island, is a biodiversity hotspot with over 90% of its wildlife found nowhere else on Earth. From playful lemurs to bizarre baobab trees, Madagascar offers unique natural wonders at every turn.</p>
                        <p>Explore rainforests filled with chameleons, relax on pristine beaches, and discover the unique cultures of this island nation. Madagascar's diverse landscapes range from tropical rainforests and coral reefs to semi-deserts and limestone formations.</p>
                        <a href="#" class="cta-button">Explore Madagascar Adventures <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Madagascar Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1575380581670-6a1d4c7b1e3a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Avenue of the Baobabs</h4>
                            <p>Witness the majestic baobab trees lining this iconic dirt road at sunset.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1622715395476-3dbcd161df05?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Andasibe-Mantadia National Park</h4>
                            <p>Hear the eerie calls of the indri lemur in this lush rainforest reserve.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1599658880436-444c5a6f90ec?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Nosy Be Island</h4>
                            <p>Relax on beautiful beaches and snorkel in crystal-clear waters teeming with marine life.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Madagascar</h3>
                    <ul>
                        <li><strong>Best time to visit:</strong> April to October (dry season)</li>
                        <li><strong>Wildlife viewing:</strong> Early morning is best for lemur spotting</li>
                        <li><strong>Health:</strong> Malaria prophylaxis recommended</li>
                        <li><strong>Currency:</strong> Malagasy Ariary (MGA)</li>
                        <li><strong>Language:</strong> French and Malagasy are widely spoken</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Zimbabwe -->
        <section id="zimbabwe" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/zw.svg" alt="Zimbabwe Flag" class="country-flag">
                    <h2 class="country-title">Zimbabwe - Land of Great Stone Cities</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1599658880436-444c5a6f90ec?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Zimbabwe, home to the magnificent Victoria Falls and ancient stone cities, offers a blend of natural wonders and rich cultural heritage. "The Smoke that Thunders" (Victoria Falls) is one of the Seven Natural Wonders of the World and a truly awe-inspiring sight.</p>
                        <p>Beyond the falls, Zimbabwe boasts excellent wildlife viewing in Hwange National Park, the historical ruins of Great Zimbabwe, and the beautiful Eastern Highlands. Experience the warmth of Zimbabwean hospitality and the country's diverse landscapes.</p>
                        <a href="#" class="cta-button">Discover Zimbabwe Safaris <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Zimbabwe Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1571167366136-b57e07761625?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Victoria Falls</h4>
                            <p>Witness one of the world's largest waterfalls, known locally as Mosi-oa-Tunya.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1626785774573-4b799315345d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Hwange National Park</h4>
                            <p>Home to one of Africa's largest elephant populations and diverse wildlife.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1559563362-c667ba5f5480?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Great Zimbabwe Ruins</h4>
                            <p>Explore the ancient stone city that gave the country its name, a UNESCO World Heritage site.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Zimbabwe</h3>
                    <ul>
                        <li><strong>Best time for falls:</strong> February to May (peak water flow)</li>
                        <li><strong>Visa:</strong> Available on arrival for most nationalities</li>
                        <li><strong>Currency:</strong> US dollars widely accepted</li>
                        <li><strong>Activities:</strong> Try white-water rafting below Victoria Falls</li>
                        <li><strong>Culture:</strong> Learn a few Shona phrases like "Mhoro" (Hello)</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <!-- Mozambique -->
        <section id="mozambique" class="country-section">
            <div class="container">
                <div class="country-header">
                    <img src="https://flagcdn.com/mz.svg" alt="Mozambique Flag" class="country-flag">
                    <h2 class="country-title">Mozambique - Tropical Paradise</h2>
                </div>
                
                <div class="country-intro">
                    <div class="country-image" style="background-image: url('https://images.unsplash.com/photo-1622715395476-3dbcd161df05?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                    <div class="country-description">
                        <p>Mozambique boasts some of Africa's most beautiful beaches with over 2,500 km of pristine coastline along the Indian Ocean. This former Portuguese colony offers a unique blend of African, Arab, and European influences.</p>
                        <p>From the vibrant capital Maputo to the idyllic islands of the Bazaruto Archipelago, Mozambique is a paradise for beach lovers, divers, and seafood enthusiasts. Discover colorful markets, colonial architecture, and world-class diving spots teeming with marine life.</p>
                        <a href="#" class="cta-button">Explore Mozambique Getaways <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <h3 class="highlight-title">Mozambique Highlights</h3>
                <div class="highlights-grid">
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1599658880436-444c5a6f90ec?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Bazaruto Archipelago</h4>
                            <p>Pristine islands with turquoise waters, perfect for diving, snorkeling, and relaxation.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1575380581670-6a1d4c7b1e3a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Maputo</h4>
                            <p>Experience the vibrant capital with its colonial architecture, markets, and seafood.</p>
                        </div>
                    </div>
                    
                    <div class="highlight-card">
                        <div class="highlight-image" style="background-image: url('https://images.unsplash.com/photo-1622715395476-3dbcd161df05?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80');"></div>
                        <div class="highlight-content">
                            <h4>Quirimbas Archipelago</h4>
                            <p>Remote islands with rich marine biodiversity and luxurious island resorts.</p>
                        </div>
                    </div>
                </div>
                
                <div class="travel-tips">
                    <h3>Travel Tips for Mozambique</h3>
                    <ul>
                        <li><strong>Best time to visit:</strong> May to November (dry season)</li>
                        <li><strong>Diving:</strong> Some of Africa's best diving with manta rays and whale sharks</li>
                        <li><strong>Cuisine:</strong> Try peri-peri prawns and matapa (cassava leaf stew)</li>
                        <li><strong>Health:</strong> Malaria prophylaxis recommended</li>
                        <li><strong>Language:</strong> Portuguese is the official language</li>
                    </ul>
                </div>
            </div>
        </section>
     
        
        <a style="visibility: hidden;display: none !important; " href="#" class="back-to-top"><i class="fas fa-arrow-up"></i></a>
    </main>
    
    <script>
        // Back to top button
        const backToTopButton = document.querySelector('.back-to-top');
        
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        });
        
        backToTopButton.addEventListener('click', (e) => {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        // Country navigation
        const navLinks = document.querySelectorAll('.country-nav a');
        
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all links
                navLinks.forEach(l => l.classList.remove('active'));
                
                // Add active class to clicked link
                this.classList.add('active');
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });
            });
        });
        
        // Highlight current section in nav
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.country-section');
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (window.pageYOffset >= sectionTop - 150) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
        
        // Navbar scroll effect
        const navbar = document.querySelector('.country-nav');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // Fade-in animation for sections
        const countrySections = document.querySelectorAll('.country-section');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, {
            threshold: 0.1
        });
        
        countrySections.forEach(section => {
            observer.observe(section);
        });
    </script>


@endsection

