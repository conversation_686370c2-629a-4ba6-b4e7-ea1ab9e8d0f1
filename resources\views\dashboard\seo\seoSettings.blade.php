@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / seo / settings list</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddPageSeo"><span>Add
                    Page seo</span></button>
        </div>
        <table id="newsletter_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 120px;">Page</th>
                    <th style="min-width: 200px;">Page h1 Heading</th>
                    <th style="min-width: 300px;">Meta Title</th>
                    <th style="min-width: 300px;">Meta Description</th>
                    <th style="min-width: 300px;">Meta Keywords</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($seoData as $seoDataList)
                <tr class="seo_row">
                    <td></td>
                    <td>{{ $seoDataList->page }}</td>
                    <td>{{ $seoDataList->page_h1_heading }}</td>
                    <td>{{ $seoDataList->meta_title }}</td>
                    <td>{{ $seoDataList->meta_description }}</td>
                    <td>{{ $seoDataList->meta_keywords }}</td>
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                               

                                <button class="update_btn edit_seo" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasUpdateSeo{{$seoDataList->id}}"
                                    data-form-id="update_seo_data_{{$seoDataList->id}}" data-id="{{$seoDataList->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                                 <button data-id="{{ $seoDataList->id }}" type="button"
                                    class="delete_btn delete_seodata">Delete</button>
                            </div>
                        </div>
                        @include("dashboard.seo.updateSeo")

                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.seo.delete")
@include("dashboard.seo.addPageSeo")


@endsection