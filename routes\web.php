<?php

use App\Http\Controllers\blogCrudController;
use App\Http\Controllers\profile\profileController;
use App\Http\Controllers\sitemapController;
use App\Http\Controllers\website\HomeController;
use App\Http\Controllers\website\paymentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
 */

Route::get('/', [HomeController::class, 'index'])->name('home');
// Route::get('/', function () {return view('website.commingSoon');});


//website xml site map
Route::get('/sitemap.xml', [sitemapController::class, 'generateSitemap']);


Route::get('/get-country-code', [HomeController::class, 'getCountryCode']);


Route::get('/home', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/destinations', [HomeController::class, 'showDestinations'])->name('destinations');
Route::get('/destination/detail/{slug}', [HomeController::class, 'destinationDetail'])->name('website.destination.detail');
//Route::get('/agent', [HomeController::class, 'getAgent'])->name('agent')->middleware('verified');
Route::get('/subscription', [HomeController::class, 'getAgent'])->name('agent')->middleware(['verified', 'auth']);
Route::get('/subscribe/{package_type}', [HomeController::class, 'subscribe'])->name('subscribe')->middleware(['verified', 'auth']);

// profile
Route::get('/user/profile', [profileController::class, 'profile'])->name('website.profile.profile')->middleware(['verified', 'auth']);
Route::get('/user/profile/change-password', [profileController::class, 'changePassword'])->name('website.profile.changePassword')->middleware(['verified', 'auth']);

Route::POST('/user/profile/update/{id}', [profileController::class, 'updateProfile']);
Route::POST('/user/profile/password/update/{id}', [profileController::class, 'updatePassword']);

Route::get('/user/profile/bookingdetails', [profileController::class, 'bookingDetail'])->name('website.profile.bookingDetail')->middleware(['verified', 'auth']);
Route::get('/user/profile/payment/history', [profileController::class, 'paymentHistory'])->name('website.profile.paymentHistory')->middleware(['verified', 'auth']);

// blog
Route::get('/blog', [blogCrudController::class, 'getBlogList'])->name('website.blogs.index');
Route::get('/blog/detail/{slug}', [blogCrudController::class, 'getBlogDetail'])->name('blog.detail');
Route::post('/api/blog/comment/submit', [blogCrudController::class, 'submitComment']);

// news
Route::get('/news', [blogCrudController::class, 'getNewsList'])->name('website.news.index');
Route::get('/news/detail/{slug}', [blogCrudController::class, 'getNewsDetail'])->name('news.detail');

//destination type page
Route::get('/destination/types', [HomeController::class, 'getDetinationTypeList'])->name('website.destinationType.destinationTypes');
Route::get('/destination/type/{slug}', [HomeController::class, 'getDetinationTypeDetail'])->name('website.destinationType.destinationTypesDetail');

Route::get('/destination/categories', [HomeController::class, 'getDetinationCategoryList'])->name('website.destinationCategory.destinationCategory');
Route::get('/destination/category/{slug}', [HomeController::class, 'getDetinationCategoryDetail'])->name('website.destinationCategory.destinationCategoryDetail');

Route::get('/destination/countries', [HomeController::class, 'getDetinationCountryList'])->name('website.destinationCountry.destinationCountry');
Route::get('/destinations/{country}', [HomeController::class, 'getDestinationsByCountry'])
    ->name('website.destinations.byCountry');

// destination booking
Route::post('/api/destination/booking/submit', [HomeController::class, 'DestinationBooking']);

// custom trip request
Route::post('/api/destination/custom/request', [HomeController::class, 'customTripRequest']);

// custom trip payment screen
Route::get('/destination-booking/payment', [HomeController::class, 'customTripPaymentView']);

// custom trip direct payment
Route::post('/api/destination/custom/payment', [paymentController::class, 'customTripDirectPayment']);

// custom trip & bookign strip payment
Route::post('/api/destination/booking/strip/payment', [paymentController::class, 'stripPayment']);
//Route::post('/api/destination/booking/strip/payment', [paymentController::class, 'createCheckoutSession']);

Route::get('/link-expired', function () {
    return view('website.customTripPayment.linkExpire');
});
Route::get('/thank-you', function () {
    return view('website.customTripPayment.thankYou');
})->name('thank-you');

Route::get('/emailtest', function () {
    return view('emailTemplates.test');
});

Route::get('/terms-and-conditions', [HomeController::class, 'termsAndConditions']);
Route::get('/privacy-policy', [HomeController::class, 'privacyPolicy']);
Route::get('/travel-guide', [HomeController::class, 'travelGuide']);
Route::get('/booking-terms-and-conditions', [HomeController::class, 'bookingTermsAndConditions']);




require __DIR__ . '/api.php';
require __DIR__ . '/auth.php';
require __DIR__ . '/dashboard.php';
