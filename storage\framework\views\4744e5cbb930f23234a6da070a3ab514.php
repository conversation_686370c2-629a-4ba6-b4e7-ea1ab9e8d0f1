
<?php $__env->startSection("wrapper"); ?>


<div class="pagination-list d-flex w-100">
    <a> / Agent / profile</a>
</div>

<?php $__currentLoopData = $subscribers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div class="card mb-4">

    <div class="user-profile-header d-flex flex-column flex-sm-row text-sm-start text-center mb-4 mt-4">
        <div class="flex-shrink-0 mt-n2 mx-sm-0 mx-auto">
            <img src="<?php echo e(asset('/storage/userImages/' . $user->profile_image)); ?>" alt="user image"
                class="d-block h-auto ms-0 ms-sm-4 rounded user-profile-img">

        </div>
        <div class="flex-grow-1 mt-3 mt-sm-5">
            <div
                class="d-flex align-items-md-end align-items-sm-start align-items-center justify-content-md-between justify-content-start mx-4 flex-md-row flex-column gap-4">
                <div class="user-profile-info">

                    <h4 class="mb-2"><?php echo e($user->name); ?></h4>
                    <ul
                        class="list-inline mb-0 d-flex align-items-center flex-wrap justify-content-sm-start justify-content-center gap-2">

                        <li class="list-inline-item fw-medium">
                            Joined : <?php echo e(customDate($sub->created_at,'F d, Y')); ?>

                        </li>
                        <!-- <li class="list-inline-item fw-medium">
                            Expire At : April 2021
                        </li> -->
                    </ul>
                </div>

                <a href="javascript:void(0)" type="button"
                    class="btn <?php echo e($sub->status == 1 ? 'btn-success' : 'btn-danger'); ?>">
                    <?php echo e($sub->status == 1 ? 'Activate' : 'Deactivate'); ?>

                </a>
            </div>
        </div>
    </div>
</div>
<div class="row g-4 mb-4 mb-4">
    <div class="col-xl-4 col-lg-6 col-md-6">
        <div class="card profile_subscription_details h-100">
            <div class="card-body">
                <small class="text-muted text-uppercase">About</small>
                <ul class="list-unstyled mb-4 mt-3">

                    <li class="d-flex align-items-center mb-3"><i class="bx bx-star"></i><span
                            class="fw-medium mx-2">Subscription:</span> <span><?php echo e($sub->subscription); ?></span></li>
                    <li class="d-flex align-items-center mb-3"><i class="bx bx-flag"></i><span
                            class="fw-medium mx-2">Country:</span> <span><?php echo e($sub->country); ?></span></li>

                </ul>
                <small class="text-muted text-uppercase">Contacts</small>
                <ul class="list-unstyled mb-4 mt-3">
                    <li class="d-flex align-items-center mb-3"><i class="bx bx-phone"></i><span
                            class="fw-medium mx-2">Contact:</span> <span><?php echo e($sub->phone_num); ?></span></li>

                    <li class="d-flex align-items-center mb-3"><i class="bx bx-envelope"></i><span
                            class="fw-medium mx-2">Email:</span> <span><?php echo e($user->email); ?></span></li>
                    <li class="d-flex align-items-center mb-3"><i class="bx bx-envelope"></i><span
                            class="fw-medium mx-2">Address:</span> <span><?php echo e($sub->address); ?></span></li>
                </ul>

            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-6 col-md-6">
        <div class="card profile_subscription_details h-100">
            <div class="card-body">
                <small class="text-muted text-uppercase">Company Details</small>
                <ul class="list-unstyled mt-3">
                    <li class="d-flex align-items-center mb-3"><span class="fw-medium mx-2">Company Name :</span>
                        <span><?php echo e($sub->company_name); ?></span>
                    </li>
                    <li class="d-flex align-items-center mb-3"><span class="fw-medium mx-2">Company Description :</span>
                        <span><?php echo e($sub->company_name); ?></span>
                    </li>
                    <li class="d-flex align-items-center mb-3"><span class="fw-medium mx-2">Registration Number :</span>
                        <span><?php echo e($sub->registration_number); ?></span>
                    </li>
                    <li class="d-flex align-items-center mb-3"><span class="fw-medium mx-2">Tin Number :</span>
                        <span><?php echo e($sub->tin_number); ?></span>
                    </li>
                    <li class="d-flex align-items-center"><span class="fw-medium mx-2">website Link :</span>
                        <span><?php echo e($sub->company_website_link); ?></span>
                    </li>
                </ul>

            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-6 col-md-6">
        <div class="card profile_subscription_details h-100">
            <div class="card-body">
                <small class="text-muted text-uppercase">Attachments</small>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <small class="text-muted text-uppercase mb-3 d-block">Company Logo</small>
                        <a href="<?php echo e(asset('/storage/companyLogo/' . $sub->company_logo)); ?>"><img
                                src="<?php echo e(asset('/storage/companyLogo/' . $sub->company_logo)); ?>" class="w-100" /></a>
                    </div>
                    <div class="col-md-6">

                        <small class="text-muted text-uppercase mb-3 d-block">Licence File</small>
                        <a href="<?php echo e(asset('/storage/licenceFiles/' . $sub->licence_file)); ?>"><img
                                src="<?php echo e(asset('/storage/licenceFiles/' . $sub->licence_file)); ?>" class="w-100 " /></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<!-- <div class="datatable_parent">
    <table id="package_listing" class="data_table display" style="width:100%">
        <thead>
            <tr>
                <th></th>
                <th>Name</th>
                <th>Position</th>
                <th>Office</th>
                <th>Age</th>
                <th>Start date</th>
                <th>Salary</th>
                <th>Salary</th>
                <th>Salary</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>
            <tr>
                <td></td>
                <td>Tiger Nixon</td>
                <td>System Architect</td>
                <td>Edinburgh</td>
                <td>61</td>
                <td>2011/04/25</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>$320,800</td>
                <td>Action</td>
            </tr>

</tbody>
</table>
</div> -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make("dashboard.include.layout", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\travelafrica\resources\views/dashboard/agents/profile.blade.php ENDPATH**/ ?>