@extends('website.include.layout')
@section('title', 'Contact Us')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')


@section('content')
<style>
.contact-error.invalid-feedback {
    background: var(--bs-form-invalid-color);
    color: #fff;
    padding: 2px 2px;
    margin-bottom: 0px;
    position: unset;
    display: block;
    float: left;
}
</style>
<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('/website/images/destinationBanner.webp');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">

                <h2>Get in Touch with Travel Africa for Your Next Adventure</h2>
                <p>Contact Travel Africa for inquiries, bookings, and assistance with planning your next international
                    adventure. Discover our wide range of destination packages and start your journey with Travel Africa
                    today.</p>
            </div>
        </div>
    </div>
</section>

<section class="login_section display-block float-left w-100">

    <div class="custom_container">
        <div class="contact_section_parent">



            <div class="login_section_box d-grid w-100 align-items-center">
                <div class="d-flex w-100 align-items-center justify-content-center w-100 flex-column">
                    <div class="login_section_box_heading d-grid w-100">
                        <h3>Have Any Question? </h3>
                        <p>Fill out the form below to reach us or call us at: {{$globalData['primary_no']}}</p>
                    </div>

                    <form method="POST" id="contact_submit" enctype="multipart/form-data" class="w-100">
                        @csrf
                        <div class="login_form_content_box">
                            <div class="login_form_field_box_parent">
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Name</span>
                                        <input id="name" type="text" placeholder="Enter your name" name="name">
                                        <label class="error" generated="true" for="name"></label>
                                    </div>

                                </div>
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Email</span>
                                        <input type="email" placeholder="Enter your email" id="email" name="email">

                                        <label class="error" generated="true" for="email"></label>
                                    </div>

                                </div>

                            </div>

                            <div class="login_form_field_box">
                                <div class="login_single_field_box">
                                    <span>phone number</span>
                                    <input id="phone" class="phone" type="text" min='0' placeholder="Enter phone"
                                        name="phone" autocomplete>

                                    <label class="error" generated="true" for="phone"></label>
                                </div>

                            </div>
                            <div class="login_form_field_box">
                                <div class="login_single_field_box textarea_field">
                                    <span>Message</span>
                                    <textarea cols="20" rows="4" id="message" name="message" required=""></textarea>
                                    <label class="error" generated="true" for="message"></label>
                                </div>

                            </div>
                            @if(config('services.recaptcha.key'))
                            <div class="g-recaptcha" id="g-recaptcha"
                                data-sitekey="{{config('services.recaptcha.key')}}">
                            </div>
                            @endif

                            <div class="login_form_signin_btn">
                                <button>Submit</button>
                            </div>
                        </div>
                    </form>

                </div>
            </div>

            <!-- banner slider -->
            <div class="contact_section_detail_parent">
                <div class="contact_list_heading">
                    <h3>Contact Info</h3>
                </div>
                <div class="contact_section_details">
                    <div class="contact_section_single_list">
                        <i class="fas fa-map-marker-alt"></i>
                        <small>{{$globalData['address']}}</small>
                    </div>
                    <div class="contact_section_single_list">
                        <i class="fas fa-envelope"></i>
                        <a class="gmail_link"
                            href="mailto:{{$globalData['contact_email']}}">{{$globalData['contact_email']}}</a>
                    </div>
                    <div class="contact_section_single_list">
                        <i class="fas fa-phone-alt"></i>
                        <a href="tel:{{$globalData['primary_no']}}">{{$globalData['primary_no']}}</a>
                    </div>
                    <div class="contact_section_single_list">
                        <i class="fas fa-phone-alt"></i>
                        <a href="tel:{{$globalData['secondary_no']}}">{{$globalData['secondary_no']}}</a>
                    </div>
                </div>
                <div class="contact_section_socail_link">
                    <a href="{{$globalData['facebook']}}"><i class="fab fa-facebook-f"></i></a>
                    <a href="{{$globalData['instagram']}}"><i class="fab fa-instagram"></i></a>
                    <a href="{{$globalData['tiktok']}}"><i class="fab fa-tiktok"></i></a>
                    <a href="{{$globalData['youtube']}}"><i class="fab fa-youtube"></i></a>
                    <a href="{{$globalData['twitter']}}"><i class="fab fa-x-twitter"></i></a>
                </div>
            </div>

        </div>
    </div>
</section>



@endsection