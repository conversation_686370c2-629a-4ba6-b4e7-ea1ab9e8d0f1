@extends('website.include.layout')
@section('title', 'Destinations by Country')

@section('meta_title', $seoData->meta_title ?? 'Destinations by Country')
@section('meta_keywords', $seoData->meta_keywords ?? 'destinations, countries, travel africa')
@section('meta_description', $seoData->meta_description ?? 'Explore destinations grouped by country')
@section('h1', $seoData->page_h1_heading ?? 'Destinations by Country')

@section('content')
<style>
.shop_sort_filter_parent > a{
    font-family: Poppins;
    font-size: 16px;
    font-weight: 500;
    line-height: 25px;
    color: var(--primary-text-color);
    box-shadow: 8px 8px 0px 0px var(--primary-bg-shadow);
    background: var(--primary-bg-color);
    border-radius: 6px !important;
    text-transform: capitalize;
    transition: 0.2s ease-in-out;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 12px;
}
.shop_sort_filter_parent h2 a , .shop_sort_filter_parent h2 strong{
    font-size: 24px;
    font-weight: 500;
    line-height: 26.66px;
    color: #1a1a1a;
    font-family: "Ancizar Serif", serif !important;
}
.shop_sort_filter_parent {
    margin-bottom: 20px;
        gap: 20px;
}   
.country_destination_box {

    background: #fdf8f3;
    padding: 20px;
}
@media(max-width:540px){
.shop_sort_filter_parent > a {
  
    font-size: 13px;
  
    box-shadow: 5px 6px 0px 0px var(--primary-bg-shadow);
  
   
    height: 38px;

    padding: 0px 12px;
}
.shop_sort_filter_parent h2 a, .shop_sort_filter_parent h2 strong {
    font-size: 20px;

}
}
</style>
<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('/website/images/typesBanner.webp');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">
                <h2>Explore Destinations by Country</h2>
                <p>Discover the best destinations across Africa, country by country.</p>
            </div>
        </div>
    </div>
</section>

<section class="d-inline-block w-100 float-left space">
    <div class="custom_container">
 <div class="d-flex flex-column gap-5">
        @foreach($destinationsByCountry as $country => $destinations)
    
<div class="country_destination_box">
        <div class="shop_sort_filter_parent d-flex align-items-center justify-content-between w-100 flex-wrap">

            <h2>
                <a href="{{ route('website.destinations.byCountry', ['country' => Str::slug($country)]) }}">
                    {{ $country }}
                </a>
                <strong>Results ({{ $destinations->count() }})</strong>
            </h2>

            <a href="{{ route('website.destinations.byCountry', ['country' => Str::slug($country)]) }}"
                class="">
                View All in {{ $country }}
            </a>
        </div>





        <div class="product_listing_box d-flex flex-column w-100">
            <div class="d-flex flex-column">

                <div class="product_listing_section row">

                    @foreach($destinations->take(4) as $destination) {{-- show first 4 as preview --}}

                    @php
                    $departureDatesTimesArray = explode(", ", $destination->departure_date_time);
                    $departureDateTimeObjects = [];
                    foreach ($departureDatesTimesArray as $dateTimeString) {
                    $dateTimeObject = \DateTime::createFromFormat('Y-m-d h:i A', $dateTimeString);
                    if ($dateTimeObject !== false) {
                    $departureDateTimeObjects[] = $dateTimeObject;
                    }
                    }

                    $currentDateTime = new \DateTime();
                    $nearestDateTime = null;
                    $nearestDiff = PHP_INT_MAX;
                    foreach ($departureDateTimeObjects as $dateTime) {
                    $diff = $dateTime->getTimestamp() - $currentDateTime->getTimestamp();
                    if ($diff > 0 && $diff < $nearestDiff) { $nearestDiff=$diff; $nearestDateTime=$dateTime; } }
                        $nearestDateString=$nearestDateTime ? $nearestDateTime->format('Y-m-d h:i A') :
                        '';
                        @endphp

                        <div class="single_product col-12 col-md-4 col-lg-4">
                            <div class="product_card w-100">
                                <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                    class="destination_img w-100 position-relative">
                                    <img src="{{$destination ? asset('storage/destinations/'.$destination->cover_img) : asset('website/images/logo.png') }}"
                                        class="w-100 h-100 object-fit-cover"
                                        alt="{{$destination->alt_text_cover_image}}" loading="lazy" decoding="async" width="350" height="250" />
                                    <div class="nearest_departure_date">
                                        {{customDate($nearestDateString,'F d, Y')}}
                                    </div>
                                </a>
                                <div class="product_content d-flex flex-column w-100">

                                    <div class="shop_single_package_rating d-flex">
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>

                                    <div class="shop_package_location d-flex align-items-start flex-column">
                                        <span class="d-flex align-items-center"><i
                                                class="far fa-calendar-check"></i>{{$destination->days}}
                                            Days - {{$destination->nights}} Nights</span>
                                        <span class="d-flex align-items-center"><i
                                                class="fas fa-map-marker-alt"></i>
                                            {{$destination->destination_country}}</span>
                                    </div>
                                    <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                        class="product_name">{{$destination->title}}</a>
                                    <div class="shop_short_des">{!! $destination->short_description !!}
                                    </div>
                                    <div class="price_tags d-flex flex-column w-100">
                                        <div class="total_and_discount_price d-flex flex-column">
                                            <span class="price_label">Per person</span>
                                            <small class="orignal_price"><span
                                                    class="price_currency text-uppercase">{{$destination->currency}}</span>
                                                {{$destination->final_price}}</small>

                                        </div>
                                        <div class="product_cart_btn">
                                            <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                                class="d-flex align-items-center justify-content-center">View
                                                Detail</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach


                </div>
            </div>

        </div>
</div>


        @endforeach
 </div>
    </div>
</section>

@endsection